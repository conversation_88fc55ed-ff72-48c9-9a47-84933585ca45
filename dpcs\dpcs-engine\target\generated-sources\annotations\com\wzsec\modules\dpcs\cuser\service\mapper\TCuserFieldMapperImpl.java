package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserField;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:52+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserFieldMapperImpl implements TCuserFieldMapper {

    @Override
    public TCuserField toEntity(TCuserFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserField tCuserField = new TCuserField();

        tCuserField.setId( dto.getId() );
        tCuserField.setDatabaseName( dto.getDatabaseName() );
        tCuserField.setTableName( dto.getTableName() );
        tCuserField.setFieldName( dto.getFieldName() );
        tCuserField.setFieldType( dto.getFieldType() );
        tCuserField.setFieldLength( dto.getFieldLength() );
        tCuserField.setFieldAccuracy( dto.getFieldAccuracy() );
        tCuserField.setCreateTime( dto.getCreateTime() );
        tCuserField.setCreateId( dto.getCreateId() );
        tCuserField.setCreateNickname( dto.getCreateNickname() );
        tCuserField.setUpdateTime( dto.getUpdateTime() );
        tCuserField.setUpdateId( dto.getUpdateId() );
        tCuserField.setUpdateNickname( dto.getUpdateNickname() );
        tCuserField.setSparefield1( dto.getSparefield1() );
        tCuserField.setSparefield2( dto.getSparefield2() );
        tCuserField.setSparefield3( dto.getSparefield3() );
        tCuserField.setSparefield4( dto.getSparefield4() );
        tCuserField.setSparefield5( dto.getSparefield5() );

        return tCuserField;
    }

    @Override
    public TCuserFieldDto toDto(TCuserField entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserFieldDto tCuserFieldDto = new TCuserFieldDto();

        tCuserFieldDto.setId( entity.getId() );
        tCuserFieldDto.setDatabaseName( entity.getDatabaseName() );
        tCuserFieldDto.setTableName( entity.getTableName() );
        tCuserFieldDto.setFieldName( entity.getFieldName() );
        tCuserFieldDto.setFieldType( entity.getFieldType() );
        tCuserFieldDto.setFieldLength( entity.getFieldLength() );
        tCuserFieldDto.setFieldAccuracy( entity.getFieldAccuracy() );
        tCuserFieldDto.setCreateTime( entity.getCreateTime() );
        tCuserFieldDto.setCreateId( entity.getCreateId() );
        tCuserFieldDto.setCreateNickname( entity.getCreateNickname() );
        tCuserFieldDto.setUpdateTime( entity.getUpdateTime() );
        tCuserFieldDto.setUpdateId( entity.getUpdateId() );
        tCuserFieldDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserFieldDto.setSparefield1( entity.getSparefield1() );
        tCuserFieldDto.setSparefield2( entity.getSparefield2() );
        tCuserFieldDto.setSparefield3( entity.getSparefield3() );
        tCuserFieldDto.setSparefield4( entity.getSparefield4() );
        tCuserFieldDto.setSparefield5( entity.getSparefield5() );

        return tCuserFieldDto;
    }

    @Override
    public List<TCuserField> toEntity(List<TCuserFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserField> list = new ArrayList<TCuserField>( dtoList.size() );
        for ( TCuserFieldDto tCuserFieldDto : dtoList ) {
            list.add( toEntity( tCuserFieldDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserFieldDto> toDto(List<TCuserField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserFieldDto> list = new ArrayList<TCuserFieldDto>( entityList.size() );
        for ( TCuserField tCuserField : entityList ) {
            list.add( toDto( tCuserField ) );
        }

        return list;
    }
}
