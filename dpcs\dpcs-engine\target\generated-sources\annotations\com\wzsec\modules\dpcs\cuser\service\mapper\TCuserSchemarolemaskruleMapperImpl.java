package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserSchemarolemaskrule;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserSchemarolemaskruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserSchemarolemaskruleMapperImpl implements TCuserSchemarolemaskruleMapper {

    @Override
    public TCuserSchemarolemaskrule toEntity(TCuserSchemarolemaskruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserSchemarolemaskrule tCuserSchemarolemaskrule = new TCuserSchemarolemaskrule();

        tCuserSchemarolemaskrule.setId( dto.getId() );
        tCuserSchemarolemaskrule.setSchemaroleId( dto.getSchemaroleId() );
        tCuserSchemarolemaskrule.setMaskruleId( dto.getMaskruleId() );

        return tCuserSchemarolemaskrule;
    }

    @Override
    public TCuserSchemarolemaskruleDto toDto(TCuserSchemarolemaskrule entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserSchemarolemaskruleDto tCuserSchemarolemaskruleDto = new TCuserSchemarolemaskruleDto();

        tCuserSchemarolemaskruleDto.setId( entity.getId() );
        tCuserSchemarolemaskruleDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserSchemarolemaskruleDto.setMaskruleId( entity.getMaskruleId() );

        return tCuserSchemarolemaskruleDto;
    }

    @Override
    public List<TCuserSchemarolemaskrule> toEntity(List<TCuserSchemarolemaskruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserSchemarolemaskrule> list = new ArrayList<TCuserSchemarolemaskrule>( dtoList.size() );
        for ( TCuserSchemarolemaskruleDto tCuserSchemarolemaskruleDto : dtoList ) {
            list.add( toEntity( tCuserSchemarolemaskruleDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserSchemarolemaskruleDto> toDto(List<TCuserSchemarolemaskrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserSchemarolemaskruleDto> list = new ArrayList<TCuserSchemarolemaskruleDto>( entityList.size() );
        for ( TCuserSchemarolemaskrule tCuserSchemarolemaskrule : entityList ) {
            list.add( toDto( tCuserSchemarolemaskrule ) );
        }

        return list;
    }
}
