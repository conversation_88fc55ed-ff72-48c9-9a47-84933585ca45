package com.wzsec.modules.dpcs.acontrol.service.mapper;

import com.wzsec.modules.dpcs.acontrol.domain.AcontrolSchemaview;
import com.wzsec.modules.dpcs.acontrol.service.dto.AcontrolSchemaviewDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AcontrolSchemaviewMapperImpl implements AcontrolSchemaviewMapper {

    @Override
    public AcontrolSchemaview toEntity(AcontrolSchemaviewDto dto) {
        if ( dto == null ) {
            return null;
        }

        AcontrolSchemaview acontrolSchemaview = new AcontrolSchemaview();

        acontrolSchemaview.setId( dto.getId() );
        acontrolSchemaview.setUsername( dto.getUsername() );
        acontrolSchemaview.setSchemaId( dto.getSchemaId() );
        acontrolSchemaview.setSchemaName( dto.getSchemaName() );
        acontrolSchemaview.setDataMode( dto.getDataMode() );
        acontrolSchemaview.setDeployMode( dto.getDeployMode() );
        acontrolSchemaview.setRowControl( dto.getRowControl() );
        acontrolSchemaview.setAuditEnabled( dto.getAuditEnabled() );
        acontrolSchemaview.setAccessControlDefault( dto.getAccessControlDefault() );
        acontrolSchemaview.setSensitiveSync( dto.getSensitiveSync() );
        acontrolSchemaview.setHighRiskApproval( dto.getHighRiskApproval() );
        acontrolSchemaview.setNote( dto.getNote() );
        acontrolSchemaview.setCreateuser( dto.getCreateuser() );
        acontrolSchemaview.setCreatetime( dto.getCreatetime() );
        acontrolSchemaview.setUpdateuser( dto.getUpdateuser() );
        acontrolSchemaview.setUpdatetime( dto.getUpdatetime() );
        acontrolSchemaview.setSparefield1( dto.getSparefield1() );
        acontrolSchemaview.setSparefield2( dto.getSparefield2() );
        acontrolSchemaview.setSparefield3( dto.getSparefield3() );
        acontrolSchemaview.setSparefield4( dto.getSparefield4() );
        acontrolSchemaview.setSparefield5( dto.getSparefield5() );

        return acontrolSchemaview;
    }

    @Override
    public AcontrolSchemaviewDto toDto(AcontrolSchemaview entity) {
        if ( entity == null ) {
            return null;
        }

        AcontrolSchemaviewDto acontrolSchemaviewDto = new AcontrolSchemaviewDto();

        acontrolSchemaviewDto.setId( entity.getId() );
        acontrolSchemaviewDto.setUsername( entity.getUsername() );
        acontrolSchemaviewDto.setSchemaId( entity.getSchemaId() );
        acontrolSchemaviewDto.setSchemaName( entity.getSchemaName() );
        acontrolSchemaviewDto.setDataMode( entity.getDataMode() );
        acontrolSchemaviewDto.setDeployMode( entity.getDeployMode() );
        acontrolSchemaviewDto.setRowControl( entity.getRowControl() );
        acontrolSchemaviewDto.setAuditEnabled( entity.getAuditEnabled() );
        acontrolSchemaviewDto.setAccessControlDefault( entity.getAccessControlDefault() );
        acontrolSchemaviewDto.setSensitiveSync( entity.getSensitiveSync() );
        acontrolSchemaviewDto.setHighRiskApproval( entity.getHighRiskApproval() );
        acontrolSchemaviewDto.setNote( entity.getNote() );
        acontrolSchemaviewDto.setCreateuser( entity.getCreateuser() );
        acontrolSchemaviewDto.setCreatetime( entity.getCreatetime() );
        acontrolSchemaviewDto.setUpdateuser( entity.getUpdateuser() );
        acontrolSchemaviewDto.setUpdatetime( entity.getUpdatetime() );
        acontrolSchemaviewDto.setSparefield1( entity.getSparefield1() );
        acontrolSchemaviewDto.setSparefield2( entity.getSparefield2() );
        acontrolSchemaviewDto.setSparefield3( entity.getSparefield3() );
        acontrolSchemaviewDto.setSparefield4( entity.getSparefield4() );
        acontrolSchemaviewDto.setSparefield5( entity.getSparefield5() );

        return acontrolSchemaviewDto;
    }

    @Override
    public List<AcontrolSchemaview> toEntity(List<AcontrolSchemaviewDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<AcontrolSchemaview> list = new ArrayList<AcontrolSchemaview>( dtoList.size() );
        for ( AcontrolSchemaviewDto acontrolSchemaviewDto : dtoList ) {
            list.add( toEntity( acontrolSchemaviewDto ) );
        }

        return list;
    }

    @Override
    public List<AcontrolSchemaviewDto> toDto(List<AcontrolSchemaview> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AcontrolSchemaviewDto> list = new ArrayList<AcontrolSchemaviewDto>( entityList.size() );
        for ( AcontrolSchemaview acontrolSchemaview : entityList ) {
            list.add( toDto( acontrolSchemaview ) );
        }

        return list;
    }
}
