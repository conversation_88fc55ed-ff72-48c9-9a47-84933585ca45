package com.wzsec.modules.dpcs.acontrol.service.mapper;

import com.wzsec.modules.dpcs.acontrol.domain.AcontrolUserview;
import com.wzsec.modules.dpcs.acontrol.service.dto.AcontrolUserviewDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AcontrolUserviewMapperImpl implements AcontrolUserviewMapper {

    @Override
    public AcontrolUserview toEntity(AcontrolUserviewDto dto) {
        if ( dto == null ) {
            return null;
        }

        AcontrolUserview acontrolUserview = new AcontrolUserview();

        acontrolUserview.setId( dto.getId() );
        acontrolUserview.setUserId( dto.getUserId() );
        acontrolUserview.setUsername( dto.getUsername() );
        acontrolUserview.setSchemaId( dto.getSchemaId() );
        acontrolUserview.setSchemaName( dto.getSchemaName() );
        acontrolUserview.setDataMode( dto.getDataMode() );
        acontrolUserview.setDeployMode( dto.getDeployMode() );
        acontrolUserview.setRowControl( dto.getRowControl() );
        acontrolUserview.setAuditEnabled( dto.getAuditEnabled() );
        acontrolUserview.setAccessControlDefault( dto.getAccessControlDefault() );
        acontrolUserview.setSensitiveSync( dto.getSensitiveSync() );
        acontrolUserview.setHighRiskApproval( dto.getHighRiskApproval() );
        acontrolUserview.setNote( dto.getNote() );
        acontrolUserview.setCreateuser( dto.getCreateuser() );
        acontrolUserview.setCreatetime( dto.getCreatetime() );
        acontrolUserview.setUpdateuser( dto.getUpdateuser() );
        acontrolUserview.setUpdatetime( dto.getUpdatetime() );
        acontrolUserview.setSparefield1( dto.getSparefield1() );
        acontrolUserview.setSparefield2( dto.getSparefield2() );
        acontrolUserview.setSparefield3( dto.getSparefield3() );
        acontrolUserview.setSparefield4( dto.getSparefield4() );
        acontrolUserview.setSparefield5( dto.getSparefield5() );

        return acontrolUserview;
    }

    @Override
    public AcontrolUserviewDto toDto(AcontrolUserview entity) {
        if ( entity == null ) {
            return null;
        }

        AcontrolUserviewDto acontrolUserviewDto = new AcontrolUserviewDto();

        acontrolUserviewDto.setId( entity.getId() );
        acontrolUserviewDto.setUserId( entity.getUserId() );
        acontrolUserviewDto.setUsername( entity.getUsername() );
        acontrolUserviewDto.setSchemaId( entity.getSchemaId() );
        acontrolUserviewDto.setSchemaName( entity.getSchemaName() );
        acontrolUserviewDto.setDataMode( entity.getDataMode() );
        acontrolUserviewDto.setDeployMode( entity.getDeployMode() );
        acontrolUserviewDto.setRowControl( entity.getRowControl() );
        acontrolUserviewDto.setAuditEnabled( entity.getAuditEnabled() );
        acontrolUserviewDto.setAccessControlDefault( entity.getAccessControlDefault() );
        acontrolUserviewDto.setSensitiveSync( entity.getSensitiveSync() );
        acontrolUserviewDto.setHighRiskApproval( entity.getHighRiskApproval() );
        acontrolUserviewDto.setNote( entity.getNote() );
        acontrolUserviewDto.setCreateuser( entity.getCreateuser() );
        acontrolUserviewDto.setCreatetime( entity.getCreatetime() );
        acontrolUserviewDto.setUpdateuser( entity.getUpdateuser() );
        acontrolUserviewDto.setUpdatetime( entity.getUpdatetime() );
        acontrolUserviewDto.setSparefield1( entity.getSparefield1() );
        acontrolUserviewDto.setSparefield2( entity.getSparefield2() );
        acontrolUserviewDto.setSparefield3( entity.getSparefield3() );
        acontrolUserviewDto.setSparefield4( entity.getSparefield4() );
        acontrolUserviewDto.setSparefield5( entity.getSparefield5() );

        return acontrolUserviewDto;
    }

    @Override
    public List<AcontrolUserview> toEntity(List<AcontrolUserviewDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<AcontrolUserview> list = new ArrayList<AcontrolUserview>( dtoList.size() );
        for ( AcontrolUserviewDto acontrolUserviewDto : dtoList ) {
            list.add( toEntity( acontrolUserviewDto ) );
        }

        return list;
    }

    @Override
    public List<AcontrolUserviewDto> toDto(List<AcontrolUserview> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AcontrolUserviewDto> list = new ArrayList<AcontrolUserviewDto>( entityList.size() );
        for ( AcontrolUserview acontrolUserview : entityList ) {
            list.add( toDto( acontrolUserview ) );
        }

        return list;
    }
}
