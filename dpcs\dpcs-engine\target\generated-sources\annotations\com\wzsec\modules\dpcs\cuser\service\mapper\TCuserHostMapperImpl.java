package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserHost;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserHostDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserHostMapperImpl implements TCuserHostMapper {

    @Override
    public TCuserHost toEntity(TCuserHostDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserHost tCuserHost = new TCuserHost();

        tCuserHost.setId( dto.getId() );
        tCuserHost.setHostName( dto.getHostName() );
        tCuserHost.setAccessType( dto.getAccessType() );
        tCuserHost.setSchemaroleId( dto.getSchemaroleId() );
        tCuserHost.setUserId( dto.getUserId() );
        tCuserHost.setRoleId( dto.getRoleId() );
        tCuserHost.setCreateTime( dto.getCreateTime() );
        tCuserHost.setCreateId( dto.getCreateId() );
        tCuserHost.setCreateNickname( dto.getCreateNickname() );
        tCuserHost.setUpdateTime( dto.getUpdateTime() );
        tCuserHost.setUpdateId( dto.getUpdateId() );
        tCuserHost.setUpdateNickname( dto.getUpdateNickname() );
        tCuserHost.setSparefield1( dto.getSparefield1() );
        tCuserHost.setSparefield2( dto.getSparefield2() );
        tCuserHost.setSparefield3( dto.getSparefield3() );
        tCuserHost.setSparefield4( dto.getSparefield4() );
        tCuserHost.setSparefield5( dto.getSparefield5() );

        return tCuserHost;
    }

    @Override
    public TCuserHostDto toDto(TCuserHost entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserHostDto tCuserHostDto = new TCuserHostDto();

        tCuserHostDto.setId( entity.getId() );
        tCuserHostDto.setHostName( entity.getHostName() );
        tCuserHostDto.setAccessType( entity.getAccessType() );
        tCuserHostDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserHostDto.setUserId( entity.getUserId() );
        tCuserHostDto.setRoleId( entity.getRoleId() );
        tCuserHostDto.setCreateTime( entity.getCreateTime() );
        tCuserHostDto.setCreateId( entity.getCreateId() );
        tCuserHostDto.setCreateNickname( entity.getCreateNickname() );
        tCuserHostDto.setUpdateTime( entity.getUpdateTime() );
        tCuserHostDto.setUpdateId( entity.getUpdateId() );
        tCuserHostDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserHostDto.setSparefield1( entity.getSparefield1() );
        tCuserHostDto.setSparefield2( entity.getSparefield2() );
        tCuserHostDto.setSparefield3( entity.getSparefield3() );
        tCuserHostDto.setSparefield4( entity.getSparefield4() );
        tCuserHostDto.setSparefield5( entity.getSparefield5() );

        return tCuserHostDto;
    }

    @Override
    public List<TCuserHost> toEntity(List<TCuserHostDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserHost> list = new ArrayList<TCuserHost>( dtoList.size() );
        for ( TCuserHostDto tCuserHostDto : dtoList ) {
            list.add( toEntity( tCuserHostDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserHostDto> toDto(List<TCuserHost> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserHostDto> list = new ArrayList<TCuserHostDto>( entityList.size() );
        for ( TCuserHost tCuserHost : entityList ) {
            list.add( toDto( tCuserHost ) );
        }

        return list;
    }
}
