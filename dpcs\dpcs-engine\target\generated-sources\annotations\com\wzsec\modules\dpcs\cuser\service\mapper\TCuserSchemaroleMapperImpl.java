package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserSchemarole;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserSchemaroleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserSchemaroleMapperImpl implements TCuserSchemaroleMapper {

    @Override
    public TCuserSchemarole toEntity(TCuserSchemaroleDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserSchemarole tCuserSchemarole = new TCuserSchemarole();

        tCuserSchemarole.setId( dto.getId() );
        tCuserSchemarole.setName( dto.getName() );
        tCuserSchemarole.setRemark( dto.getRemark() );
        tCuserSchemarole.setDeploymentMode( dto.getDeploymentMode() );
        tCuserSchemarole.setAuditSwitch( dto.getAuditSwitch() );
        tCuserSchemarole.setSensitiveSyn( dto.getSensitiveSyn() );
        tCuserSchemarole.setMaskSwitch( dto.getMaskSwitch() );
        tCuserSchemarole.setAccessControl( dto.getAccessControl() );
        tCuserSchemarole.setCreateTime( dto.getCreateTime() );
        tCuserSchemarole.setCreateId( dto.getCreateId() );
        tCuserSchemarole.setCreateNickname( dto.getCreateNickname() );
        tCuserSchemarole.setUpdateTime( dto.getUpdateTime() );
        tCuserSchemarole.setUpdateId( dto.getUpdateId() );
        tCuserSchemarole.setUpdateNickname( dto.getUpdateNickname() );
        tCuserSchemarole.setSparefield1( dto.getSparefield1() );
        tCuserSchemarole.setSparefield2( dto.getSparefield2() );
        tCuserSchemarole.setSparefield3( dto.getSparefield3() );
        tCuserSchemarole.setSparefield4( dto.getSparefield4() );
        tCuserSchemarole.setSparefield5( dto.getSparefield5() );

        return tCuserSchemarole;
    }

    @Override
    public TCuserSchemaroleDto toDto(TCuserSchemarole entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserSchemaroleDto tCuserSchemaroleDto = new TCuserSchemaroleDto();

        tCuserSchemaroleDto.setId( entity.getId() );
        tCuserSchemaroleDto.setName( entity.getName() );
        tCuserSchemaroleDto.setRemark( entity.getRemark() );
        tCuserSchemaroleDto.setDeploymentMode( entity.getDeploymentMode() );
        tCuserSchemaroleDto.setAuditSwitch( entity.getAuditSwitch() );
        tCuserSchemaroleDto.setSensitiveSyn( entity.getSensitiveSyn() );
        tCuserSchemaroleDto.setMaskSwitch( entity.getMaskSwitch() );
        tCuserSchemaroleDto.setAccessControl( entity.getAccessControl() );
        tCuserSchemaroleDto.setCreateTime( entity.getCreateTime() );
        tCuserSchemaroleDto.setCreateId( entity.getCreateId() );
        tCuserSchemaroleDto.setCreateNickname( entity.getCreateNickname() );
        tCuserSchemaroleDto.setUpdateTime( entity.getUpdateTime() );
        tCuserSchemaroleDto.setUpdateId( entity.getUpdateId() );
        tCuserSchemaroleDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserSchemaroleDto.setSparefield1( entity.getSparefield1() );
        tCuserSchemaroleDto.setSparefield2( entity.getSparefield2() );
        tCuserSchemaroleDto.setSparefield3( entity.getSparefield3() );
        tCuserSchemaroleDto.setSparefield4( entity.getSparefield4() );
        tCuserSchemaroleDto.setSparefield5( entity.getSparefield5() );

        return tCuserSchemaroleDto;
    }

    @Override
    public List<TCuserSchemarole> toEntity(List<TCuserSchemaroleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserSchemarole> list = new ArrayList<TCuserSchemarole>( dtoList.size() );
        for ( TCuserSchemaroleDto tCuserSchemaroleDto : dtoList ) {
            list.add( toEntity( tCuserSchemaroleDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserSchemaroleDto> toDto(List<TCuserSchemarole> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserSchemaroleDto> list = new ArrayList<TCuserSchemaroleDto>( entityList.size() );
        for ( TCuserSchemarole tCuserSchemarole : entityList ) {
            list.add( toDto( tCuserSchemarole ) );
        }

        return list;
    }
}
