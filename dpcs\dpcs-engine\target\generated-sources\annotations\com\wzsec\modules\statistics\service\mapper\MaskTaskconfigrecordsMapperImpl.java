package com.wzsec.modules.statistics.service.mapper;

import com.wzsec.modules.statistics.domain.MaskTaskconfigrecords;
import com.wzsec.modules.statistics.service.dto.MaskTaskconfigrecordsDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskTaskconfigrecordsMapperImpl implements MaskTaskconfigrecordsMapper {

    @Override
    public MaskTaskconfigrecords toEntity(MaskTaskconfigrecordsDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskTaskconfigrecords maskTaskconfigrecords = new MaskTaskconfigrecords();

        maskTaskconfigrecords.setId( dto.getId() );
        maskTaskconfigrecords.setTaskname( dto.getTaskname() );
        maskTaskconfigrecords.setTasktype( dto.getTasktype() );
        maskTaskconfigrecords.setOperation( dto.getOperation() );
        maskTaskconfigrecords.setCreateuser( dto.getCreateuser() );
        maskTaskconfigrecords.setCreatetime( dto.getCreatetime() );
        maskTaskconfigrecords.setSparefield1( dto.getSparefield1() );
        maskTaskconfigrecords.setSparefield2( dto.getSparefield2() );
        maskTaskconfigrecords.setSparefield3( dto.getSparefield3() );
        maskTaskconfigrecords.setSparefield4( dto.getSparefield4() );

        return maskTaskconfigrecords;
    }

    @Override
    public MaskTaskconfigrecordsDto toDto(MaskTaskconfigrecords entity) {
        if ( entity == null ) {
            return null;
        }

        MaskTaskconfigrecordsDto maskTaskconfigrecordsDto = new MaskTaskconfigrecordsDto();

        maskTaskconfigrecordsDto.setId( entity.getId() );
        maskTaskconfigrecordsDto.setTaskname( entity.getTaskname() );
        maskTaskconfigrecordsDto.setTasktype( entity.getTasktype() );
        maskTaskconfigrecordsDto.setOperation( entity.getOperation() );
        maskTaskconfigrecordsDto.setCreateuser( entity.getCreateuser() );
        maskTaskconfigrecordsDto.setCreatetime( entity.getCreatetime() );
        maskTaskconfigrecordsDto.setSparefield1( entity.getSparefield1() );
        maskTaskconfigrecordsDto.setSparefield2( entity.getSparefield2() );
        maskTaskconfigrecordsDto.setSparefield3( entity.getSparefield3() );
        maskTaskconfigrecordsDto.setSparefield4( entity.getSparefield4() );

        return maskTaskconfigrecordsDto;
    }

    @Override
    public List<MaskTaskconfigrecords> toEntity(List<MaskTaskconfigrecordsDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskTaskconfigrecords> list = new ArrayList<MaskTaskconfigrecords>( dtoList.size() );
        for ( MaskTaskconfigrecordsDto maskTaskconfigrecordsDto : dtoList ) {
            list.add( toEntity( maskTaskconfigrecordsDto ) );
        }

        return list;
    }

    @Override
    public List<MaskTaskconfigrecordsDto> toDto(List<MaskTaskconfigrecords> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskTaskconfigrecordsDto> list = new ArrayList<MaskTaskconfigrecordsDto>( entityList.size() );
        for ( MaskTaskconfigrecords maskTaskconfigrecords : entityList ) {
            list.add( toDto( maskTaskconfigrecords ) );
        }

        return list;
    }
}
