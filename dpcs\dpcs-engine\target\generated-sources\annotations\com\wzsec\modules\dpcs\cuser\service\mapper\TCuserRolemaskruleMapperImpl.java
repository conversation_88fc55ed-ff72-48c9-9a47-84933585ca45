package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserRolemaskrule;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserRolemaskruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserRolemaskruleMapperImpl implements TCuserRolemaskruleMapper {

    @Override
    public TCuserRolemaskrule toEntity(TCuserRolemaskruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserRolemaskrule tCuserRolemaskrule = new TCuserRolemaskrule();

        tCuserRolemaskrule.setId( dto.getId() );
        tCuserRolemaskrule.setRoleId( dto.getRoleId() );
        tCuserRolemaskrule.setMaskruleId( dto.getMaskruleId() );

        return tCuserRolemaskrule;
    }

    @Override
    public TCuserRolemaskruleDto toDto(TCuserRolemaskrule entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserRolemaskruleDto tCuserRolemaskruleDto = new TCuserRolemaskruleDto();

        tCuserRolemaskruleDto.setId( entity.getId() );
        tCuserRolemaskruleDto.setRoleId( entity.getRoleId() );
        tCuserRolemaskruleDto.setMaskruleId( entity.getMaskruleId() );

        return tCuserRolemaskruleDto;
    }

    @Override
    public List<TCuserRolemaskrule> toEntity(List<TCuserRolemaskruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserRolemaskrule> list = new ArrayList<TCuserRolemaskrule>( dtoList.size() );
        for ( TCuserRolemaskruleDto tCuserRolemaskruleDto : dtoList ) {
            list.add( toEntity( tCuserRolemaskruleDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserRolemaskruleDto> toDto(List<TCuserRolemaskrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserRolemaskruleDto> list = new ArrayList<TCuserRolemaskruleDto>( entityList.size() );
        for ( TCuserRolemaskrule tCuserRolemaskrule : entityList ) {
            list.add( toDto( tCuserRolemaskrule ) );
        }

        return list;
    }
}
