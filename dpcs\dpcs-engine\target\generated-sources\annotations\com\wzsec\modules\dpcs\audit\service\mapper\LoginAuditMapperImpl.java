package com.wzsec.modules.dpcs.audit.service.mapper;

import com.wzsec.modules.dpcs.audit.domain.LoginAudit;
import com.wzsec.modules.dpcs.audit.service.dto.LoginAuditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class LoginAuditMapperImpl implements LoginAuditMapper {

    @Override
    public LoginAudit toEntity(LoginAuditDto dto) {
        if ( dto == null ) {
            return null;
        }

        LoginAudit loginAudit = new LoginAudit();

        loginAudit.setId( dto.getId() );
        loginAudit.setUserAccount( dto.getUserAccount() );
        loginAudit.setSchemaName( dto.getSchemaName() );
        loginAudit.setIp( dto.getIp() );
        loginAudit.setHostname( dto.getHostname() );
        loginAudit.setLoginTime( dto.getLoginTime() );
        loginAudit.setResponseAction( dto.getResponseAction() );
        loginAudit.setSparefield1( dto.getSparefield1() );
        loginAudit.setSparefield2( dto.getSparefield2() );
        loginAudit.setSparefield3( dto.getSparefield3() );
        loginAudit.setSparefield4( dto.getSparefield4() );
        loginAudit.setSparefield5( dto.getSparefield5() );

        return loginAudit;
    }

    @Override
    public LoginAuditDto toDto(LoginAudit entity) {
        if ( entity == null ) {
            return null;
        }

        LoginAuditDto loginAuditDto = new LoginAuditDto();

        loginAuditDto.setId( entity.getId() );
        loginAuditDto.setUserAccount( entity.getUserAccount() );
        loginAuditDto.setSchemaName( entity.getSchemaName() );
        loginAuditDto.setIp( entity.getIp() );
        loginAuditDto.setHostname( entity.getHostname() );
        loginAuditDto.setLoginTime( entity.getLoginTime() );
        loginAuditDto.setResponseAction( entity.getResponseAction() );
        loginAuditDto.setSparefield1( entity.getSparefield1() );
        loginAuditDto.setSparefield2( entity.getSparefield2() );
        loginAuditDto.setSparefield3( entity.getSparefield3() );
        loginAuditDto.setSparefield4( entity.getSparefield4() );
        loginAuditDto.setSparefield5( entity.getSparefield5() );

        return loginAuditDto;
    }

    @Override
    public List<LoginAudit> toEntity(List<LoginAuditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<LoginAudit> list = new ArrayList<LoginAudit>( dtoList.size() );
        for ( LoginAuditDto loginAuditDto : dtoList ) {
            list.add( toEntity( loginAuditDto ) );
        }

        return list;
    }

    @Override
    public List<LoginAuditDto> toDto(List<LoginAudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<LoginAuditDto> list = new ArrayList<LoginAuditDto>( entityList.size() );
        for ( LoginAudit loginAudit : entityList ) {
            list.add( toDto( loginAudit ) );
        }

        return list;
    }
}
