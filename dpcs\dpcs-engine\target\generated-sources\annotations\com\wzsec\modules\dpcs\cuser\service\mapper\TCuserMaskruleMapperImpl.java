package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserMaskrule;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserMaskruleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserMaskruleMapperImpl implements TCuserMaskruleMapper {

    @Override
    public TCuserMaskrule toEntity(TCuserMaskruleDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserMaskrule tCuserMaskrule = new TCuserMaskrule();

        tCuserMaskrule.setId( dto.getId() );
        tCuserMaskrule.setMaskruleId( dto.getMaskruleId() );
        tCuserMaskrule.setName( dto.getName() );
        tCuserMaskrule.setRuleType( dto.getRuleType() );
        tCuserMaskrule.setOperatingMode( dto.getOperatingMode() );
        tCuserMaskrule.setCover( dto.getCover() );
        tCuserMaskrule.setCoverSymbol( dto.getCoverSymbol() );
        tCuserMaskrule.setDescribe( dto.getDescribe() );
        tCuserMaskrule.setCreateTime( dto.getCreateTime() );
        tCuserMaskrule.setCreateId( dto.getCreateId() );
        tCuserMaskrule.setCreateNickname( dto.getCreateNickname() );
        tCuserMaskrule.setUpdateTime( dto.getUpdateTime() );
        tCuserMaskrule.setUpdateId( dto.getUpdateId() );
        tCuserMaskrule.setUpdateNickname( dto.getUpdateNickname() );
        tCuserMaskrule.setSparefield1( dto.getSparefield1() );
        tCuserMaskrule.setSparefield2( dto.getSparefield2() );
        tCuserMaskrule.setSparefield3( dto.getSparefield3() );
        tCuserMaskrule.setSparefield4( dto.getSparefield4() );
        tCuserMaskrule.setSparefield5( dto.getSparefield5() );

        return tCuserMaskrule;
    }

    @Override
    public TCuserMaskruleDto toDto(TCuserMaskrule entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserMaskruleDto tCuserMaskruleDto = new TCuserMaskruleDto();

        tCuserMaskruleDto.setId( entity.getId() );
        tCuserMaskruleDto.setMaskruleId( entity.getMaskruleId() );
        tCuserMaskruleDto.setName( entity.getName() );
        tCuserMaskruleDto.setRuleType( entity.getRuleType() );
        tCuserMaskruleDto.setOperatingMode( entity.getOperatingMode() );
        tCuserMaskruleDto.setCover( entity.getCover() );
        tCuserMaskruleDto.setCoverSymbol( entity.getCoverSymbol() );
        tCuserMaskruleDto.setDescribe( entity.getDescribe() );
        tCuserMaskruleDto.setCreateTime( entity.getCreateTime() );
        tCuserMaskruleDto.setCreateId( entity.getCreateId() );
        tCuserMaskruleDto.setCreateNickname( entity.getCreateNickname() );
        tCuserMaskruleDto.setUpdateTime( entity.getUpdateTime() );
        tCuserMaskruleDto.setUpdateId( entity.getUpdateId() );
        tCuserMaskruleDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserMaskruleDto.setSparefield1( entity.getSparefield1() );
        tCuserMaskruleDto.setSparefield2( entity.getSparefield2() );
        tCuserMaskruleDto.setSparefield3( entity.getSparefield3() );
        tCuserMaskruleDto.setSparefield4( entity.getSparefield4() );
        tCuserMaskruleDto.setSparefield5( entity.getSparefield5() );

        return tCuserMaskruleDto;
    }

    @Override
    public List<TCuserMaskrule> toEntity(List<TCuserMaskruleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserMaskrule> list = new ArrayList<TCuserMaskrule>( dtoList.size() );
        for ( TCuserMaskruleDto tCuserMaskruleDto : dtoList ) {
            list.add( toEntity( tCuserMaskruleDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserMaskruleDto> toDto(List<TCuserMaskrule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserMaskruleDto> list = new ArrayList<TCuserMaskruleDto>( entityList.size() );
        for ( TCuserMaskrule tCuserMaskrule : entityList ) {
            list.add( toDto( tCuserMaskrule ) );
        }

        return list;
    }
}
