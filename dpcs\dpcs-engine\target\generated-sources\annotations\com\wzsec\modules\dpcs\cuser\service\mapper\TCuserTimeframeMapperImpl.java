package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserTimeframe;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserTimeframeDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserTimeframeMapperImpl implements TCuserTimeframeMapper {

    @Override
    public TCuserTimeframe toEntity(TCuserTimeframeDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserTimeframe tCuserTimeframe = new TCuserTimeframe();

        tCuserTimeframe.setId( dto.getId() );
        tCuserTimeframe.setStartTime( dto.getStartTime() );
        tCuserTimeframe.setEndTime( dto.getEndTime() );
        tCuserTimeframe.setAccessType( dto.getAccessType() );
        tCuserTimeframe.setSchemaroleId( dto.getSchemaroleId() );
        tCuserTimeframe.setUserId( dto.getUserId() );
        tCuserTimeframe.setRoleId( dto.getRoleId() );
        tCuserTimeframe.setCreateTime( dto.getCreateTime() );
        tCuserTimeframe.setCreateId( dto.getCreateId() );
        tCuserTimeframe.setCreateNickname( dto.getCreateNickname() );
        tCuserTimeframe.setUpdateTime( dto.getUpdateTime() );
        tCuserTimeframe.setUpdateId( dto.getUpdateId() );
        tCuserTimeframe.setUpdateNickname( dto.getUpdateNickname() );
        tCuserTimeframe.setSparefield1( dto.getSparefield1() );
        tCuserTimeframe.setSparefield2( dto.getSparefield2() );
        tCuserTimeframe.setSparefield3( dto.getSparefield3() );
        tCuserTimeframe.setSparefield4( dto.getSparefield4() );
        tCuserTimeframe.setSparefield5( dto.getSparefield5() );

        return tCuserTimeframe;
    }

    @Override
    public TCuserTimeframeDto toDto(TCuserTimeframe entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserTimeframeDto tCuserTimeframeDto = new TCuserTimeframeDto();

        tCuserTimeframeDto.setId( entity.getId() );
        tCuserTimeframeDto.setStartTime( entity.getStartTime() );
        tCuserTimeframeDto.setEndTime( entity.getEndTime() );
        tCuserTimeframeDto.setAccessType( entity.getAccessType() );
        tCuserTimeframeDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserTimeframeDto.setUserId( entity.getUserId() );
        tCuserTimeframeDto.setRoleId( entity.getRoleId() );
        tCuserTimeframeDto.setCreateTime( entity.getCreateTime() );
        tCuserTimeframeDto.setCreateId( entity.getCreateId() );
        tCuserTimeframeDto.setCreateNickname( entity.getCreateNickname() );
        tCuserTimeframeDto.setUpdateTime( entity.getUpdateTime() );
        tCuserTimeframeDto.setUpdateId( entity.getUpdateId() );
        tCuserTimeframeDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserTimeframeDto.setSparefield1( entity.getSparefield1() );
        tCuserTimeframeDto.setSparefield2( entity.getSparefield2() );
        tCuserTimeframeDto.setSparefield3( entity.getSparefield3() );
        tCuserTimeframeDto.setSparefield4( entity.getSparefield4() );
        tCuserTimeframeDto.setSparefield5( entity.getSparefield5() );

        return tCuserTimeframeDto;
    }

    @Override
    public List<TCuserTimeframe> toEntity(List<TCuserTimeframeDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserTimeframe> list = new ArrayList<TCuserTimeframe>( dtoList.size() );
        for ( TCuserTimeframeDto tCuserTimeframeDto : dtoList ) {
            list.add( toEntity( tCuserTimeframeDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserTimeframeDto> toDto(List<TCuserTimeframe> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserTimeframeDto> list = new ArrayList<TCuserTimeframeDto>( entityList.size() );
        for ( TCuserTimeframe tCuserTimeframe : entityList ) {
            list.add( toDto( tCuserTimeframe ) );
        }

        return list;
    }
}
