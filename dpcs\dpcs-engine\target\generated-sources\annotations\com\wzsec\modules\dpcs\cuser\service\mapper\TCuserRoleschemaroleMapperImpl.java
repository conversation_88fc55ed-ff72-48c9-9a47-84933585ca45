package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserRoleschemarole;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserRoleschemaroleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserRoleschemaroleMapperImpl implements TCuserRoleschemaroleMapper {

    @Override
    public TCuserRoleschemarole toEntity(TCuserRoleschemaroleDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserRoleschemarole tCuserRoleschemarole = new TCuserRoleschemarole();

        tCuserRoleschemarole.setId( dto.getId() );
        tCuserRoleschemarole.setRoleId( dto.getRoleId() );
        tCuserRoleschemarole.setSchemaRoleId( dto.getSchemaRoleId() );

        return tCuserRoleschemarole;
    }

    @Override
    public TCuserRoleschemaroleDto toDto(TCuserRoleschemarole entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserRoleschemaroleDto tCuserRoleschemaroleDto = new TCuserRoleschemaroleDto();

        tCuserRoleschemaroleDto.setId( entity.getId() );
        tCuserRoleschemaroleDto.setRoleId( entity.getRoleId() );
        tCuserRoleschemaroleDto.setSchemaRoleId( entity.getSchemaRoleId() );

        return tCuserRoleschemaroleDto;
    }

    @Override
    public List<TCuserRoleschemarole> toEntity(List<TCuserRoleschemaroleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserRoleschemarole> list = new ArrayList<TCuserRoleschemarole>( dtoList.size() );
        for ( TCuserRoleschemaroleDto tCuserRoleschemaroleDto : dtoList ) {
            list.add( toEntity( tCuserRoleschemaroleDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserRoleschemaroleDto> toDto(List<TCuserRoleschemarole> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserRoleschemaroleDto> list = new ArrayList<TCuserRoleschemaroleDto>( entityList.size() );
        for ( TCuserRoleschemarole tCuserRoleschemarole : entityList ) {
            list.add( toDto( tCuserRoleschemarole ) );
        }

        return list;
    }
}
