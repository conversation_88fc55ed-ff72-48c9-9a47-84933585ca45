package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.PermissionRole;
import com.wzsec.modules.dpcs.cuser.service.dto.PermissionRoleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class PermissionRoleMapperImpl implements PermissionRoleMapper {

    @Override
    public PermissionRole toEntity(PermissionRoleDto dto) {
        if ( dto == null ) {
            return null;
        }

        PermissionRole permissionRole = new PermissionRole();

        permissionRole.setId( dto.getId() );
        permissionRole.setName( dto.getName() );
        permissionRole.setRemark( dto.getRemark() );
        permissionRole.setCreateTime( dto.getCreateTime() );
        permissionRole.setCreateId( dto.getCreateId() );
        permissionRole.setCreateNickname( dto.getCreateNickname() );
        permissionRole.setUpdateTime( dto.getUpdateTime() );
        permissionRole.setUpdateId( dto.getUpdateId() );
        permissionRole.setUpdateNickname( dto.getUpdateNickname() );
        permissionRole.setSparefield1( dto.getSparefield1() );
        permissionRole.setSparefield2( dto.getSparefield2() );
        permissionRole.setSparefield3( dto.getSparefield3() );
        permissionRole.setSparefield4( dto.getSparefield4() );
        permissionRole.setSparefield5( dto.getSparefield5() );
        permissionRole.setDataPattern( dto.getDataPattern() );
        permissionRole.setRowNumberControl( dto.getRowNumberControl() );
        permissionRole.setAdmissionControl( dto.getAdmissionControl() );
        permissionRole.setHighRiskApproval( dto.getHighRiskApproval() );
        permissionRole.setFrequencySwitch( dto.getFrequencySwitch() );
        permissionRole.setDeploymentMode( dto.getDeploymentMode() );
        permissionRole.setAuditSwitch( dto.getAuditSwitch() );
        permissionRole.setSensitiveSyn( dto.getSensitiveSyn() );
        permissionRole.setMaskSwitch( dto.getMaskSwitch() );
        permissionRole.setAccessControl( dto.getAccessControl() );

        return permissionRole;
    }

    @Override
    public PermissionRoleDto toDto(PermissionRole entity) {
        if ( entity == null ) {
            return null;
        }

        PermissionRoleDto permissionRoleDto = new PermissionRoleDto();

        permissionRoleDto.setId( entity.getId() );
        permissionRoleDto.setName( entity.getName() );
        permissionRoleDto.setRemark( entity.getRemark() );
        permissionRoleDto.setCreateTime( entity.getCreateTime() );
        permissionRoleDto.setCreateId( entity.getCreateId() );
        permissionRoleDto.setCreateNickname( entity.getCreateNickname() );
        permissionRoleDto.setUpdateTime( entity.getUpdateTime() );
        permissionRoleDto.setUpdateId( entity.getUpdateId() );
        permissionRoleDto.setUpdateNickname( entity.getUpdateNickname() );
        permissionRoleDto.setSparefield1( entity.getSparefield1() );
        permissionRoleDto.setSparefield2( entity.getSparefield2() );
        permissionRoleDto.setSparefield3( entity.getSparefield3() );
        permissionRoleDto.setSparefield4( entity.getSparefield4() );
        permissionRoleDto.setSparefield5( entity.getSparefield5() );
        permissionRoleDto.setDataPattern( entity.getDataPattern() );
        permissionRoleDto.setRowNumberControl( entity.getRowNumberControl() );
        permissionRoleDto.setAdmissionControl( entity.getAdmissionControl() );
        permissionRoleDto.setHighRiskApproval( entity.getHighRiskApproval() );
        permissionRoleDto.setFrequencySwitch( entity.getFrequencySwitch() );
        permissionRoleDto.setDeploymentMode( entity.getDeploymentMode() );
        permissionRoleDto.setAuditSwitch( entity.getAuditSwitch() );
        permissionRoleDto.setSensitiveSyn( entity.getSensitiveSyn() );
        permissionRoleDto.setMaskSwitch( entity.getMaskSwitch() );
        permissionRoleDto.setAccessControl( entity.getAccessControl() );

        return permissionRoleDto;
    }

    @Override
    public List<PermissionRole> toEntity(List<PermissionRoleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<PermissionRole> list = new ArrayList<PermissionRole>( dtoList.size() );
        for ( PermissionRoleDto permissionRoleDto : dtoList ) {
            list.add( toEntity( permissionRoleDto ) );
        }

        return list;
    }

    @Override
    public List<PermissionRoleDto> toDto(List<PermissionRole> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<PermissionRoleDto> list = new ArrayList<PermissionRoleDto>( entityList.size() );
        for ( PermissionRole permissionRole : entityList ) {
            list.add( toDto( permissionRole ) );
        }

        return list;
    }
}
