package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserPermissiontemplate;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserPermissiontemplateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserPermissiontemplateMapperImpl implements TCuserPermissiontemplateMapper {

    @Override
    public TCuserPermissiontemplate toEntity(TCuserPermissiontemplateDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserPermissiontemplate tCuserPermissiontemplate = new TCuserPermissiontemplate();

        tCuserPermissiontemplate.setId( dto.getId() );
        tCuserPermissiontemplate.setName( dto.getName() );
        tCuserPermissiontemplate.setRemark( dto.getRemark() );
        tCuserPermissiontemplate.setCreateTime( dto.getCreateTime() );
        tCuserPermissiontemplate.setCreateId( dto.getCreateId() );
        tCuserPermissiontemplate.setCreateNickname( dto.getCreateNickname() );
        tCuserPermissiontemplate.setUpdateTime( dto.getUpdateTime() );
        tCuserPermissiontemplate.setUpdateId( dto.getUpdateId() );
        tCuserPermissiontemplate.setUpdateNickname( dto.getUpdateNickname() );
        tCuserPermissiontemplate.setSparefield1( dto.getSparefield1() );
        tCuserPermissiontemplate.setSparefield2( dto.getSparefield2() );
        tCuserPermissiontemplate.setSparefield3( dto.getSparefield3() );
        tCuserPermissiontemplate.setSparefield4( dto.getSparefield4() );
        tCuserPermissiontemplate.setSparefield5( dto.getSparefield5() );

        return tCuserPermissiontemplate;
    }

    @Override
    public TCuserPermissiontemplateDto toDto(TCuserPermissiontemplate entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserPermissiontemplateDto tCuserPermissiontemplateDto = new TCuserPermissiontemplateDto();

        tCuserPermissiontemplateDto.setId( entity.getId() );
        tCuserPermissiontemplateDto.setName( entity.getName() );
        tCuserPermissiontemplateDto.setRemark( entity.getRemark() );
        tCuserPermissiontemplateDto.setCreateTime( entity.getCreateTime() );
        tCuserPermissiontemplateDto.setCreateId( entity.getCreateId() );
        tCuserPermissiontemplateDto.setCreateNickname( entity.getCreateNickname() );
        tCuserPermissiontemplateDto.setUpdateTime( entity.getUpdateTime() );
        tCuserPermissiontemplateDto.setUpdateId( entity.getUpdateId() );
        tCuserPermissiontemplateDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserPermissiontemplateDto.setSparefield1( entity.getSparefield1() );
        tCuserPermissiontemplateDto.setSparefield2( entity.getSparefield2() );
        tCuserPermissiontemplateDto.setSparefield3( entity.getSparefield3() );
        tCuserPermissiontemplateDto.setSparefield4( entity.getSparefield4() );
        tCuserPermissiontemplateDto.setSparefield5( entity.getSparefield5() );

        return tCuserPermissiontemplateDto;
    }

    @Override
    public List<TCuserPermissiontemplate> toEntity(List<TCuserPermissiontemplateDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserPermissiontemplate> list = new ArrayList<TCuserPermissiontemplate>( dtoList.size() );
        for ( TCuserPermissiontemplateDto tCuserPermissiontemplateDto : dtoList ) {
            list.add( toEntity( tCuserPermissiontemplateDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserPermissiontemplateDto> toDto(List<TCuserPermissiontemplate> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserPermissiontemplateDto> list = new ArrayList<TCuserPermissiontemplateDto>( entityList.size() );
        for ( TCuserPermissiontemplate tCuserPermissiontemplate : entityList ) {
            list.add( toDto( tCuserPermissiontemplate ) );
        }

        return list;
    }
}
