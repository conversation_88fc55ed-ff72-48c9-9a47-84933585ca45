package com.wzsec.modules.dpcs.audit.service.mapper;

import com.wzsec.modules.dpcs.audit.domain.SqlinjectionRule;
import com.wzsec.modules.dpcs.audit.service.dto.SqlinjectionRuleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SqlinjectionRuleMapperImpl implements SqlinjectionRuleMapper {

    @Override
    public SqlinjectionRule toEntity(SqlinjectionRuleDto dto) {
        if ( dto == null ) {
            return null;
        }

        SqlinjectionRule sqlinjectionRule = new SqlinjectionRule();

        sqlinjectionRule.setId( dto.getId() );
        sqlinjectionRule.setRulename( dto.getRulename() );
        sqlinjectionRule.setPattern( dto.getPattern() );
        sqlinjectionRule.setDescription( dto.getDescription() );
        sqlinjectionRule.setEnabled( dto.getEnabled() );
        sqlinjectionRule.setRisk( dto.getRisk() );
        sqlinjectionRule.setCreateuser( dto.getCreateuser() );
        sqlinjectionRule.setCreatetime( dto.getCreatetime() );
        sqlinjectionRule.setUpdateuser( dto.getUpdateuser() );
        sqlinjectionRule.setUpdatetime( dto.getUpdatetime() );
        sqlinjectionRule.setSparefield1( dto.getSparefield1() );
        sqlinjectionRule.setSparefield2( dto.getSparefield2() );
        sqlinjectionRule.setSparefield3( dto.getSparefield3() );
        sqlinjectionRule.setSparefield4( dto.getSparefield4() );
        sqlinjectionRule.setSparefield5( dto.getSparefield5() );

        return sqlinjectionRule;
    }

    @Override
    public SqlinjectionRuleDto toDto(SqlinjectionRule entity) {
        if ( entity == null ) {
            return null;
        }

        SqlinjectionRuleDto sqlinjectionRuleDto = new SqlinjectionRuleDto();

        sqlinjectionRuleDto.setId( entity.getId() );
        sqlinjectionRuleDto.setRulename( entity.getRulename() );
        sqlinjectionRuleDto.setPattern( entity.getPattern() );
        sqlinjectionRuleDto.setDescription( entity.getDescription() );
        sqlinjectionRuleDto.setEnabled( entity.getEnabled() );
        sqlinjectionRuleDto.setRisk( entity.getRisk() );
        sqlinjectionRuleDto.setCreateuser( entity.getCreateuser() );
        sqlinjectionRuleDto.setCreatetime( entity.getCreatetime() );
        sqlinjectionRuleDto.setUpdateuser( entity.getUpdateuser() );
        sqlinjectionRuleDto.setUpdatetime( entity.getUpdatetime() );
        sqlinjectionRuleDto.setSparefield1( entity.getSparefield1() );
        sqlinjectionRuleDto.setSparefield2( entity.getSparefield2() );
        sqlinjectionRuleDto.setSparefield3( entity.getSparefield3() );
        sqlinjectionRuleDto.setSparefield4( entity.getSparefield4() );
        sqlinjectionRuleDto.setSparefield5( entity.getSparefield5() );

        return sqlinjectionRuleDto;
    }

    @Override
    public List<SqlinjectionRule> toEntity(List<SqlinjectionRuleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SqlinjectionRule> list = new ArrayList<SqlinjectionRule>( dtoList.size() );
        for ( SqlinjectionRuleDto sqlinjectionRuleDto : dtoList ) {
            list.add( toEntity( sqlinjectionRuleDto ) );
        }

        return list;
    }

    @Override
    public List<SqlinjectionRuleDto> toDto(List<SqlinjectionRule> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SqlinjectionRuleDto> list = new ArrayList<SqlinjectionRuleDto>( entityList.size() );
        for ( SqlinjectionRule sqlinjectionRule : entityList ) {
            list.add( toDto( sqlinjectionRule ) );
        }

        return list;
    }
}
