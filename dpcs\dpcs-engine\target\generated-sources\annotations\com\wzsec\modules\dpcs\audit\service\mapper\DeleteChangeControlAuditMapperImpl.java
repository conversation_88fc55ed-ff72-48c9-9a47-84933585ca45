package com.wzsec.modules.dpcs.audit.service.mapper;

import com.wzsec.modules.dpcs.audit.domain.DeleteChangeControlAudit;
import com.wzsec.modules.dpcs.audit.service.dto.DeleteChangeControlAuditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class DeleteChangeControlAuditMapperImpl implements DeleteChangeControlAuditMapper {

    @Override
    public DeleteChangeControlAudit toEntity(DeleteChangeControlAuditDto dto) {
        if ( dto == null ) {
            return null;
        }

        DeleteChangeControlAudit deleteChangeControlAudit = new DeleteChangeControlAudit();

        deleteChangeControlAudit.setId( dto.getId() );
        deleteChangeControlAudit.setUserAccount( dto.getUserAccount() );
        deleteChangeControlAudit.setSchemaName( dto.getSchemaName() );
        deleteChangeControlAudit.setAccessTime( dto.getAccessTime() );
        deleteChangeControlAudit.setSqlStatement( dto.getSqlStatement() );
        deleteChangeControlAudit.setNumberRows( dto.getNumberRows() );
        deleteChangeControlAudit.setUserprivilege( dto.getUserprivilege() );
        deleteChangeControlAudit.setSparefield1( dto.getSparefield1() );
        deleteChangeControlAudit.setSparefield2( dto.getSparefield2() );
        deleteChangeControlAudit.setSparefield3( dto.getSparefield3() );
        deleteChangeControlAudit.setSparefield4( dto.getSparefield4() );
        deleteChangeControlAudit.setSparefield5( dto.getSparefield5() );

        return deleteChangeControlAudit;
    }

    @Override
    public DeleteChangeControlAuditDto toDto(DeleteChangeControlAudit entity) {
        if ( entity == null ) {
            return null;
        }

        DeleteChangeControlAuditDto deleteChangeControlAuditDto = new DeleteChangeControlAuditDto();

        deleteChangeControlAuditDto.setId( entity.getId() );
        deleteChangeControlAuditDto.setUserAccount( entity.getUserAccount() );
        deleteChangeControlAuditDto.setSchemaName( entity.getSchemaName() );
        deleteChangeControlAuditDto.setAccessTime( entity.getAccessTime() );
        deleteChangeControlAuditDto.setSqlStatement( entity.getSqlStatement() );
        deleteChangeControlAuditDto.setNumberRows( entity.getNumberRows() );
        deleteChangeControlAuditDto.setUserprivilege( entity.getUserprivilege() );
        deleteChangeControlAuditDto.setSparefield1( entity.getSparefield1() );
        deleteChangeControlAuditDto.setSparefield2( entity.getSparefield2() );
        deleteChangeControlAuditDto.setSparefield3( entity.getSparefield3() );
        deleteChangeControlAuditDto.setSparefield4( entity.getSparefield4() );
        deleteChangeControlAuditDto.setSparefield5( entity.getSparefield5() );

        return deleteChangeControlAuditDto;
    }

    @Override
    public List<DeleteChangeControlAudit> toEntity(List<DeleteChangeControlAuditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<DeleteChangeControlAudit> list = new ArrayList<DeleteChangeControlAudit>( dtoList.size() );
        for ( DeleteChangeControlAuditDto deleteChangeControlAuditDto : dtoList ) {
            list.add( toEntity( deleteChangeControlAuditDto ) );
        }

        return list;
    }

    @Override
    public List<DeleteChangeControlAuditDto> toDto(List<DeleteChangeControlAudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<DeleteChangeControlAuditDto> list = new ArrayList<DeleteChangeControlAuditDto>( entityList.size() );
        for ( DeleteChangeControlAudit deleteChangeControlAudit : entityList ) {
            list.add( toDto( deleteChangeControlAudit ) );
        }

        return list;
    }
}
