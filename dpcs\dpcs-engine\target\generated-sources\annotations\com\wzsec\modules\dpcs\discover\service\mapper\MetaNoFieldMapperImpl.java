package com.wzsec.modules.dpcs.discover.service.mapper;

import com.wzsec.modules.dpcs.discover.domain.MetaNoField;
import com.wzsec.modules.dpcs.discover.service.dto.MetaNoFieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MetaNoFieldMapperImpl implements MetaNoFieldMapper {

    @Override
    public MetaNoField toEntity(MetaNoFieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaNoField metaNoField = new MetaNoField();

        metaNoField.setId( dto.getId() );
        metaNoField.setMtid( dto.getMtid() );
        metaNoField.setIp( dto.getIp() );
        metaNoField.setPort( dto.getPort() );
        metaNoField.setResult( dto.getResult() );
        metaNoField.setLevel( dto.getLevel() );
        metaNoField.setCreateuser( dto.getCreateuser() );
        metaNoField.setCreatetime( dto.getCreatetime() );
        metaNoField.setUpdateuser( dto.getUpdateuser() );
        metaNoField.setUpdatetime( dto.getUpdatetime() );
        metaNoField.setSparefield1( dto.getSparefield1() );
        metaNoField.setSparefield2( dto.getSparefield2() );
        metaNoField.setSparefield3( dto.getSparefield3() );
        metaNoField.setSparefield4( dto.getSparefield4() );

        return metaNoField;
    }

    @Override
    public MetaNoFieldDto toDto(MetaNoField entity) {
        if ( entity == null ) {
            return null;
        }

        MetaNoFieldDto metaNoFieldDto = new MetaNoFieldDto();

        metaNoFieldDto.setId( entity.getId() );
        metaNoFieldDto.setMtid( entity.getMtid() );
        metaNoFieldDto.setIp( entity.getIp() );
        metaNoFieldDto.setPort( entity.getPort() );
        metaNoFieldDto.setResult( entity.getResult() );
        metaNoFieldDto.setLevel( entity.getLevel() );
        metaNoFieldDto.setCreateuser( entity.getCreateuser() );
        metaNoFieldDto.setCreatetime( entity.getCreatetime() );
        metaNoFieldDto.setUpdateuser( entity.getUpdateuser() );
        metaNoFieldDto.setUpdatetime( entity.getUpdatetime() );
        metaNoFieldDto.setSparefield1( entity.getSparefield1() );
        metaNoFieldDto.setSparefield2( entity.getSparefield2() );
        metaNoFieldDto.setSparefield3( entity.getSparefield3() );
        metaNoFieldDto.setSparefield4( entity.getSparefield4() );

        return metaNoFieldDto;
    }

    @Override
    public List<MetaNoField> toEntity(List<MetaNoFieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaNoField> list = new ArrayList<MetaNoField>( dtoList.size() );
        for ( MetaNoFieldDto metaNoFieldDto : dtoList ) {
            list.add( toEntity( metaNoFieldDto ) );
        }

        return list;
    }

    @Override
    public List<MetaNoFieldDto> toDto(List<MetaNoField> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaNoFieldDto> list = new ArrayList<MetaNoFieldDto>( entityList.size() );
        for ( MetaNoField metaNoField : entityList ) {
            list.add( toDto( metaNoField ) );
        }

        return list;
    }
}
