package com.wzsec.modules.dpcs.maskmanage.service.mapper;

import com.wzsec.modules.dpcs.maskmanage.domain.TMaskDbtaskconfig;
import com.wzsec.modules.dpcs.maskmanage.service.dto.TMaskDbtaskconfigDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TMaskDbtaskconfigMapperImpl implements TMaskDbtaskconfigMapper {

    @Override
    public TMaskDbtaskconfig toEntity(TMaskDbtaskconfigDto dto) {
        if ( dto == null ) {
            return null;
        }

        TMaskDbtaskconfig tMaskDbtaskconfig = new TMaskDbtaskconfig();

        tMaskDbtaskconfig.setId( dto.getId() );
        tMaskDbtaskconfig.setName( dto.getName() );
        tMaskDbtaskconfig.setInputschemaId( dto.getInputschemaId() );
        tMaskDbtaskconfig.setInputschemaName( dto.getInputschemaName() );
        tMaskDbtaskconfig.setUserId( dto.getUserId() );
        tMaskDbtaskconfig.setUsername( dto.getUsername() );
        tMaskDbtaskconfig.setScheduling( dto.getScheduling() );
        tMaskDbtaskconfig.setCron( dto.getCron() );
        tMaskDbtaskconfig.setKeyField( dto.getKeyField() );
        tMaskDbtaskconfig.setRecordNumber( dto.getRecordNumber() );
        tMaskDbtaskconfig.setMaskNumber( dto.getMaskNumber() );
        tMaskDbtaskconfig.setCompletionPercentage( dto.getCompletionPercentage() );
        tMaskDbtaskconfig.setStartTime( dto.getStartTime() );
        tMaskDbtaskconfig.setEndTime( dto.getEndTime() );
        tMaskDbtaskconfig.setState( dto.getState() );
        tMaskDbtaskconfig.setOutputschemaId( dto.getOutputschemaId() );
        tMaskDbtaskconfig.setOutputschemaName( dto.getOutputschemaName() );
        tMaskDbtaskconfig.setExecutionstate( dto.getExecutionstate() );
        tMaskDbtaskconfig.setCreateTime( dto.getCreateTime() );
        tMaskDbtaskconfig.setCreateId( dto.getCreateId() );
        tMaskDbtaskconfig.setCreateNickname( dto.getCreateNickname() );
        tMaskDbtaskconfig.setUpdateTime( dto.getUpdateTime() );
        tMaskDbtaskconfig.setUpdateId( dto.getUpdateId() );
        tMaskDbtaskconfig.setUpdateNickname( dto.getUpdateNickname() );
        tMaskDbtaskconfig.setSparefield1( dto.getSparefield1() );
        tMaskDbtaskconfig.setSparefield2( dto.getSparefield2() );
        tMaskDbtaskconfig.setSparefield3( dto.getSparefield3() );
        tMaskDbtaskconfig.setSparefield4( dto.getSparefield4() );
        tMaskDbtaskconfig.setSparefield5( dto.getSparefield5() );

        return tMaskDbtaskconfig;
    }

    @Override
    public TMaskDbtaskconfigDto toDto(TMaskDbtaskconfig entity) {
        if ( entity == null ) {
            return null;
        }

        TMaskDbtaskconfigDto tMaskDbtaskconfigDto = new TMaskDbtaskconfigDto();

        tMaskDbtaskconfigDto.setId( entity.getId() );
        tMaskDbtaskconfigDto.setName( entity.getName() );
        tMaskDbtaskconfigDto.setInputschemaId( entity.getInputschemaId() );
        tMaskDbtaskconfigDto.setInputschemaName( entity.getInputschemaName() );
        tMaskDbtaskconfigDto.setCreateTime( entity.getCreateTime() );
        tMaskDbtaskconfigDto.setUserId( entity.getUserId() );
        tMaskDbtaskconfigDto.setUsername( entity.getUsername() );
        tMaskDbtaskconfigDto.setScheduling( entity.getScheduling() );
        tMaskDbtaskconfigDto.setCron( entity.getCron() );
        tMaskDbtaskconfigDto.setKeyField( entity.getKeyField() );
        tMaskDbtaskconfigDto.setRecordNumber( entity.getRecordNumber() );
        tMaskDbtaskconfigDto.setMaskNumber( entity.getMaskNumber() );
        tMaskDbtaskconfigDto.setCompletionPercentage( entity.getCompletionPercentage() );
        tMaskDbtaskconfigDto.setStartTime( entity.getStartTime() );
        tMaskDbtaskconfigDto.setEndTime( entity.getEndTime() );
        tMaskDbtaskconfigDto.setState( entity.getState() );
        tMaskDbtaskconfigDto.setOutputschemaId( entity.getOutputschemaId() );
        tMaskDbtaskconfigDto.setOutputschemaName( entity.getOutputschemaName() );
        tMaskDbtaskconfigDto.setExecutionstate( entity.getExecutionstate() );
        tMaskDbtaskconfigDto.setCreateId( entity.getCreateId() );
        tMaskDbtaskconfigDto.setCreateNickname( entity.getCreateNickname() );
        tMaskDbtaskconfigDto.setUpdateTime( entity.getUpdateTime() );
        tMaskDbtaskconfigDto.setUpdateId( entity.getUpdateId() );
        tMaskDbtaskconfigDto.setUpdateNickname( entity.getUpdateNickname() );
        tMaskDbtaskconfigDto.setSparefield1( entity.getSparefield1() );
        tMaskDbtaskconfigDto.setSparefield2( entity.getSparefield2() );
        tMaskDbtaskconfigDto.setSparefield3( entity.getSparefield3() );
        tMaskDbtaskconfigDto.setSparefield4( entity.getSparefield4() );
        tMaskDbtaskconfigDto.setSparefield5( entity.getSparefield5() );

        return tMaskDbtaskconfigDto;
    }

    @Override
    public List<TMaskDbtaskconfig> toEntity(List<TMaskDbtaskconfigDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TMaskDbtaskconfig> list = new ArrayList<TMaskDbtaskconfig>( dtoList.size() );
        for ( TMaskDbtaskconfigDto tMaskDbtaskconfigDto : dtoList ) {
            list.add( toEntity( tMaskDbtaskconfigDto ) );
        }

        return list;
    }

    @Override
    public List<TMaskDbtaskconfigDto> toDto(List<TMaskDbtaskconfig> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TMaskDbtaskconfigDto> list = new ArrayList<TMaskDbtaskconfigDto>( entityList.size() );
        for ( TMaskDbtaskconfig tMaskDbtaskconfig : entityList ) {
            list.add( toDto( tMaskDbtaskconfig ) );
        }

        return list;
    }
}
