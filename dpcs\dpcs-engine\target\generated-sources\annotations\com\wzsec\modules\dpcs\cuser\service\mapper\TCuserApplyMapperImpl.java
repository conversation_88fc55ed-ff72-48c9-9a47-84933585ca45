package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserApply;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserApplyDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserApplyMapperImpl implements TCuserApplyMapper {

    @Override
    public TCuserApply toEntity(TCuserApplyDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserApply tCuserApply = new TCuserApply();

        tCuserApply.setId( dto.getId() );
        tCuserApply.setApplyName( dto.getApplyName() );
        tCuserApply.setAccessType( dto.getAccessType() );
        tCuserApply.setSchemaroleId( dto.getSchemaroleId() );
        tCuserApply.setUserId( dto.getUserId() );
        tCuserApply.setRoleId( dto.getRoleId() );
        tCuserApply.setCreateTime( dto.getCreateTime() );
        tCuserApply.setCreateId( dto.getCreateId() );
        tCuserApply.setCreateNickname( dto.getCreateNickname() );
        tCuserApply.setUpdateTime( dto.getUpdateTime() );
        tCuserApply.setUpdateId( dto.getUpdateId() );
        tCuserApply.setUpdateNickname( dto.getUpdateNickname() );
        tCuserApply.setSparefield1( dto.getSparefield1() );
        tCuserApply.setSparefield2( dto.getSparefield2() );
        tCuserApply.setSparefield3( dto.getSparefield3() );
        tCuserApply.setSparefield4( dto.getSparefield4() );
        tCuserApply.setSparefield5( dto.getSparefield5() );

        return tCuserApply;
    }

    @Override
    public TCuserApplyDto toDto(TCuserApply entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserApplyDto tCuserApplyDto = new TCuserApplyDto();

        tCuserApplyDto.setId( entity.getId() );
        tCuserApplyDto.setApplyName( entity.getApplyName() );
        tCuserApplyDto.setAccessType( entity.getAccessType() );
        tCuserApplyDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserApplyDto.setUserId( entity.getUserId() );
        tCuserApplyDto.setRoleId( entity.getRoleId() );
        tCuserApplyDto.setCreateTime( entity.getCreateTime() );
        tCuserApplyDto.setCreateId( entity.getCreateId() );
        tCuserApplyDto.setCreateNickname( entity.getCreateNickname() );
        tCuserApplyDto.setUpdateTime( entity.getUpdateTime() );
        tCuserApplyDto.setUpdateId( entity.getUpdateId() );
        tCuserApplyDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserApplyDto.setSparefield1( entity.getSparefield1() );
        tCuserApplyDto.setSparefield2( entity.getSparefield2() );
        tCuserApplyDto.setSparefield3( entity.getSparefield3() );
        tCuserApplyDto.setSparefield4( entity.getSparefield4() );
        tCuserApplyDto.setSparefield5( entity.getSparefield5() );

        return tCuserApplyDto;
    }

    @Override
    public List<TCuserApply> toEntity(List<TCuserApplyDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserApply> list = new ArrayList<TCuserApply>( dtoList.size() );
        for ( TCuserApplyDto tCuserApplyDto : dtoList ) {
            list.add( toEntity( tCuserApplyDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserApplyDto> toDto(List<TCuserApply> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserApplyDto> list = new ArrayList<TCuserApplyDto>( entityList.size() );
        for ( TCuserApply tCuserApply : entityList ) {
            list.add( toDto( tCuserApply ) );
        }

        return list;
    }
}
