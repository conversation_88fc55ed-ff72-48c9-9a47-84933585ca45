package com.wzsec.modules.dpcs.audit.service.mapper;

import com.wzsec.modules.dpcs.audit.domain.VisitAudit;
import com.wzsec.modules.dpcs.audit.service.dto.VisitAuditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class VisitAuditMapperImpl implements VisitAuditMapper {

    @Override
    public VisitAudit toEntity(VisitAuditDto dto) {
        if ( dto == null ) {
            return null;
        }

        VisitAudit visitAudit = new VisitAudit();

        visitAudit.setId( dto.getId() );
        visitAudit.setUserAccount( dto.getUserAccount() );
        visitAudit.setSchemaName( dto.getSchemaName() );
        visitAudit.setSqltype( dto.getSqltype() );
        visitAudit.setExecutionDuration( dto.getExecutionDuration() );
        visitAudit.setAccessTime( dto.getAccessTime() );
        visitAudit.setResponseAction( dto.getResponseAction() );
        visitAudit.setSqlDescription( dto.getSqlDescription() );
        visitAudit.setSqlStatement( dto.getSqlStatement() );
        visitAudit.setSparefield1( dto.getSparefield1() );
        visitAudit.setSparefield2( dto.getSparefield2() );
        visitAudit.setSparefield3( dto.getSparefield3() );
        visitAudit.setSparefield4( dto.getSparefield4() );
        visitAudit.setSparefield5( dto.getSparefield5() );

        return visitAudit;
    }

    @Override
    public VisitAuditDto toDto(VisitAudit entity) {
        if ( entity == null ) {
            return null;
        }

        VisitAuditDto visitAuditDto = new VisitAuditDto();

        visitAuditDto.setId( entity.getId() );
        visitAuditDto.setUserAccount( entity.getUserAccount() );
        visitAuditDto.setSchemaName( entity.getSchemaName() );
        visitAuditDto.setSqltype( entity.getSqltype() );
        visitAuditDto.setExecutionDuration( entity.getExecutionDuration() );
        visitAuditDto.setAccessTime( entity.getAccessTime() );
        visitAuditDto.setResponseAction( entity.getResponseAction() );
        visitAuditDto.setSqlDescription( entity.getSqlDescription() );
        visitAuditDto.setSqlStatement( entity.getSqlStatement() );
        visitAuditDto.setSparefield1( entity.getSparefield1() );
        visitAuditDto.setSparefield2( entity.getSparefield2() );
        visitAuditDto.setSparefield3( entity.getSparefield3() );
        visitAuditDto.setSparefield4( entity.getSparefield4() );
        visitAuditDto.setSparefield5( entity.getSparefield5() );

        return visitAuditDto;
    }

    @Override
    public List<VisitAudit> toEntity(List<VisitAuditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<VisitAudit> list = new ArrayList<VisitAudit>( dtoList.size() );
        for ( VisitAuditDto visitAuditDto : dtoList ) {
            list.add( toEntity( visitAuditDto ) );
        }

        return list;
    }

    @Override
    public List<VisitAuditDto> toDto(List<VisitAudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<VisitAuditDto> list = new ArrayList<VisitAuditDto>( entityList.size() );
        for ( VisitAudit visitAudit : entityList ) {
            list.add( toDto( visitAudit ) );
        }

        return list;
    }
}
