package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.PermissionUserRole;
import com.wzsec.modules.dpcs.cuser.service.dto.PermissionUserRoleDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class PermissionUserRoleMapperImpl implements PermissionUserRoleMapper {

    @Override
    public PermissionUserRole toEntity(PermissionUserRoleDto dto) {
        if ( dto == null ) {
            return null;
        }

        PermissionUserRole permissionUserRole = new PermissionUserRole();

        permissionUserRole.setId( dto.getId() );
        permissionUserRole.setPermissionUserId( dto.getPermissionUserId() );
        permissionUserRole.setPermissionRoleId( dto.getPermissionRoleId() );

        return permissionUserRole;
    }

    @Override
    public PermissionUserRoleDto toDto(PermissionUserRole entity) {
        if ( entity == null ) {
            return null;
        }

        PermissionUserRoleDto permissionUserRoleDto = new PermissionUserRoleDto();

        permissionUserRoleDto.setId( entity.getId() );
        permissionUserRoleDto.setPermissionUserId( entity.getPermissionUserId() );
        permissionUserRoleDto.setPermissionRoleId( entity.getPermissionRoleId() );

        return permissionUserRoleDto;
    }

    @Override
    public List<PermissionUserRole> toEntity(List<PermissionUserRoleDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<PermissionUserRole> list = new ArrayList<PermissionUserRole>( dtoList.size() );
        for ( PermissionUserRoleDto permissionUserRoleDto : dtoList ) {
            list.add( toEntity( permissionUserRoleDto ) );
        }

        return list;
    }

    @Override
    public List<PermissionUserRoleDto> toDto(List<PermissionUserRole> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<PermissionUserRoleDto> list = new ArrayList<PermissionUserRoleDto>( entityList.size() );
        for ( PermissionUserRole permissionUserRole : entityList ) {
            list.add( toDto( permissionUserRole ) );
        }

        return list;
    }
}
