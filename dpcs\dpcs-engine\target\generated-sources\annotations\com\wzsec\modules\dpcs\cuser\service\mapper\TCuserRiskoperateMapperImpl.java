package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserRiskoperate;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserRiskoperateDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserRiskoperateMapperImpl implements TCuserRiskoperateMapper {

    @Override
    public TCuserRiskoperate toEntity(TCuserRiskoperateDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserRiskoperate tCuserRiskoperate = new TCuserRiskoperate();

        tCuserRiskoperate.setId( dto.getId() );
        tCuserRiskoperate.setUserId( dto.getUserId() );
        tCuserRiskoperate.setUsername( dto.getUsername() );
        tCuserRiskoperate.setNickname( dto.getNickname() );
        tCuserRiskoperate.setSchemaroleId( dto.getSchemaroleId() );
        tCuserRiskoperate.setSchemaroleName( dto.getSchemaroleName() );
        tCuserRiskoperate.setDeptId( dto.getDeptId() );
        tCuserRiskoperate.setDeptName( dto.getDeptName() );
        tCuserRiskoperate.setStatementType( dto.getStatementType() );
        tCuserRiskoperate.setStatementSql( dto.getStatementSql() );
        tCuserRiskoperate.setParam( dto.getParam() );
        tCuserRiskoperate.setRowsNumber( dto.getRowsNumber() );
        tCuserRiskoperate.setApplyFor( dto.getApplyFor() );
        tCuserRiskoperate.setNote( dto.getNote() );
        tCuserRiskoperate.setApprovalResult( dto.getApprovalResult() );
        tCuserRiskoperate.setApplicationTime( dto.getApplicationTime() );
        tCuserRiskoperate.setApprovalTime( dto.getApprovalTime() );
        tCuserRiskoperate.setStartExecutionTime( dto.getStartExecutionTime() );
        tCuserRiskoperate.setEndExecutionTime( dto.getEndExecutionTime() );
        tCuserRiskoperate.setApproverId( dto.getApproverId() );
        tCuserRiskoperate.setApproverName( dto.getApproverName() );
        tCuserRiskoperate.setCreateTime( dto.getCreateTime() );
        tCuserRiskoperate.setCreateId( dto.getCreateId() );
        tCuserRiskoperate.setCreateNickname( dto.getCreateNickname() );
        tCuserRiskoperate.setUpdateTime( dto.getUpdateTime() );
        tCuserRiskoperate.setUpdateId( dto.getUpdateId() );
        tCuserRiskoperate.setUpdateNickname( dto.getUpdateNickname() );
        tCuserRiskoperate.setSparefield1( dto.getSparefield1() );
        tCuserRiskoperate.setSparefield2( dto.getSparefield2() );
        tCuserRiskoperate.setSparefield3( dto.getSparefield3() );
        tCuserRiskoperate.setSparefield4( dto.getSparefield4() );
        tCuserRiskoperate.setSparefield5( dto.getSparefield5() );

        return tCuserRiskoperate;
    }

    @Override
    public TCuserRiskoperateDto toDto(TCuserRiskoperate entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserRiskoperateDto tCuserRiskoperateDto = new TCuserRiskoperateDto();

        tCuserRiskoperateDto.setId( entity.getId() );
        tCuserRiskoperateDto.setUserId( entity.getUserId() );
        tCuserRiskoperateDto.setUsername( entity.getUsername() );
        tCuserRiskoperateDto.setNickname( entity.getNickname() );
        tCuserRiskoperateDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserRiskoperateDto.setSchemaroleName( entity.getSchemaroleName() );
        tCuserRiskoperateDto.setDeptId( entity.getDeptId() );
        tCuserRiskoperateDto.setDeptName( entity.getDeptName() );
        tCuserRiskoperateDto.setStatementType( entity.getStatementType() );
        tCuserRiskoperateDto.setStatementSql( entity.getStatementSql() );
        tCuserRiskoperateDto.setParam( entity.getParam() );
        tCuserRiskoperateDto.setRowsNumber( entity.getRowsNumber() );
        tCuserRiskoperateDto.setApplyFor( entity.getApplyFor() );
        tCuserRiskoperateDto.setNote( entity.getNote() );
        tCuserRiskoperateDto.setApprovalResult( entity.getApprovalResult() );
        tCuserRiskoperateDto.setApplicationTime( entity.getApplicationTime() );
        tCuserRiskoperateDto.setApprovalTime( entity.getApprovalTime() );
        tCuserRiskoperateDto.setStartExecutionTime( entity.getStartExecutionTime() );
        tCuserRiskoperateDto.setEndExecutionTime( entity.getEndExecutionTime() );
        tCuserRiskoperateDto.setApproverId( entity.getApproverId() );
        tCuserRiskoperateDto.setApproverName( entity.getApproverName() );
        tCuserRiskoperateDto.setCreateTime( entity.getCreateTime() );
        tCuserRiskoperateDto.setCreateId( entity.getCreateId() );
        tCuserRiskoperateDto.setCreateNickname( entity.getCreateNickname() );
        tCuserRiskoperateDto.setUpdateTime( entity.getUpdateTime() );
        tCuserRiskoperateDto.setUpdateId( entity.getUpdateId() );
        tCuserRiskoperateDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserRiskoperateDto.setSparefield1( entity.getSparefield1() );
        tCuserRiskoperateDto.setSparefield2( entity.getSparefield2() );
        tCuserRiskoperateDto.setSparefield3( entity.getSparefield3() );
        tCuserRiskoperateDto.setSparefield4( entity.getSparefield4() );
        tCuserRiskoperateDto.setSparefield5( entity.getSparefield5() );

        return tCuserRiskoperateDto;
    }

    @Override
    public List<TCuserRiskoperate> toEntity(List<TCuserRiskoperateDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserRiskoperate> list = new ArrayList<TCuserRiskoperate>( dtoList.size() );
        for ( TCuserRiskoperateDto tCuserRiskoperateDto : dtoList ) {
            list.add( toEntity( tCuserRiskoperateDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserRiskoperateDto> toDto(List<TCuserRiskoperate> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserRiskoperateDto> list = new ArrayList<TCuserRiskoperateDto>( entityList.size() );
        for ( TCuserRiskoperate tCuserRiskoperate : entityList ) {
            list.add( toDto( tCuserRiskoperate ) );
        }

        return list;
    }
}
