package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserRulefield;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserRulefieldDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserRulefieldMapperImpl implements TCuserRulefieldMapper {

    @Override
    public TCuserRulefield toEntity(TCuserRulefieldDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserRulefield tCuserRulefield = new TCuserRulefield();

        tCuserRulefield.setId( dto.getId() );
        tCuserRulefield.setMaskruleId( dto.getMaskruleId() );
        tCuserRulefield.setFieldId( dto.getFieldId() );

        return tCuserRulefield;
    }

    @Override
    public TCuserRulefieldDto toDto(TCuserRulefield entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserRulefieldDto tCuserRulefieldDto = new TCuserRulefieldDto();

        tCuserRulefieldDto.setId( entity.getId() );
        tCuserRulefieldDto.setMaskruleId( entity.getMaskruleId() );
        tCuserRulefieldDto.setFieldId( entity.getFieldId() );

        return tCuserRulefieldDto;
    }

    @Override
    public List<TCuserRulefield> toEntity(List<TCuserRulefieldDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserRulefield> list = new ArrayList<TCuserRulefield>( dtoList.size() );
        for ( TCuserRulefieldDto tCuserRulefieldDto : dtoList ) {
            list.add( toEntity( tCuserRulefieldDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserRulefieldDto> toDto(List<TCuserRulefield> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserRulefieldDto> list = new ArrayList<TCuserRulefieldDto>( entityList.size() );
        for ( TCuserRulefield tCuserRulefield : entityList ) {
            list.add( toDto( tCuserRulefield ) );
        }

        return list;
    }
}
