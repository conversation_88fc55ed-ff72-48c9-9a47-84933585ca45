package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserHazardlevel;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserHazardlevelDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserHazardlevelMapperImpl implements TCuserHazardlevelMapper {

    @Override
    public TCuserHazardlevel toEntity(TCuserHazardlevelDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserHazardlevel tCuserHazardlevel = new TCuserHazardlevel();

        tCuserHazardlevel.setId( dto.getId() );
        tCuserHazardlevel.setHazardLevel( dto.getHazardLevel() );
        tCuserHazardlevel.setRemark( dto.getRemark() );
        tCuserHazardlevel.setCreateTime( dto.getCreateTime() );
        tCuserHazardlevel.setCreateId( dto.getCreateId() );
        tCuserHazardlevel.setCreateNickname( dto.getCreateNickname() );
        tCuserHazardlevel.setUpdateTime( dto.getUpdateTime() );
        tCuserHazardlevel.setUpdateId( dto.getUpdateId() );
        tCuserHazardlevel.setUpdateNickname( dto.getUpdateNickname() );
        tCuserHazardlevel.setSparefield1( dto.getSparefield1() );
        tCuserHazardlevel.setSparefield2( dto.getSparefield2() );
        tCuserHazardlevel.setSparefield3( dto.getSparefield3() );
        tCuserHazardlevel.setSparefield4( dto.getSparefield4() );
        tCuserHazardlevel.setSparefield5( dto.getSparefield5() );

        return tCuserHazardlevel;
    }

    @Override
    public TCuserHazardlevelDto toDto(TCuserHazardlevel entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserHazardlevelDto tCuserHazardlevelDto = new TCuserHazardlevelDto();

        tCuserHazardlevelDto.setId( entity.getId() );
        tCuserHazardlevelDto.setHazardLevel( entity.getHazardLevel() );
        tCuserHazardlevelDto.setRemark( entity.getRemark() );
        tCuserHazardlevelDto.setCreateTime( entity.getCreateTime() );
        tCuserHazardlevelDto.setCreateId( entity.getCreateId() );
        tCuserHazardlevelDto.setCreateNickname( entity.getCreateNickname() );
        tCuserHazardlevelDto.setUpdateTime( entity.getUpdateTime() );
        tCuserHazardlevelDto.setUpdateId( entity.getUpdateId() );
        tCuserHazardlevelDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserHazardlevelDto.setSparefield1( entity.getSparefield1() );
        tCuserHazardlevelDto.setSparefield2( entity.getSparefield2() );
        tCuserHazardlevelDto.setSparefield3( entity.getSparefield3() );
        tCuserHazardlevelDto.setSparefield4( entity.getSparefield4() );
        tCuserHazardlevelDto.setSparefield5( entity.getSparefield5() );

        return tCuserHazardlevelDto;
    }

    @Override
    public List<TCuserHazardlevel> toEntity(List<TCuserHazardlevelDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserHazardlevel> list = new ArrayList<TCuserHazardlevel>( dtoList.size() );
        for ( TCuserHazardlevelDto tCuserHazardlevelDto : dtoList ) {
            list.add( toEntity( tCuserHazardlevelDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserHazardlevelDto> toDto(List<TCuserHazardlevel> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserHazardlevelDto> list = new ArrayList<TCuserHazardlevelDto>( entityList.size() );
        for ( TCuserHazardlevel tCuserHazardlevel : entityList ) {
            list.add( toDto( tCuserHazardlevel ) );
        }

        return list;
    }
}
