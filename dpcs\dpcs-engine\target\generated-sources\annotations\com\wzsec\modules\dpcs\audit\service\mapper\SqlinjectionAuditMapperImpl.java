package com.wzsec.modules.dpcs.audit.service.mapper;

import com.wzsec.modules.dpcs.audit.domain.SqlinjectionAudit;
import com.wzsec.modules.dpcs.audit.service.dto.SqlinjectionAuditDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:52+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class SqlinjectionAuditMapperImpl implements SqlinjectionAuditMapper {

    @Override
    public SqlinjectionAudit toEntity(SqlinjectionAuditDto dto) {
        if ( dto == null ) {
            return null;
        }

        SqlinjectionAudit sqlinjectionAudit = new SqlinjectionAudit();

        sqlinjectionAudit.setId( dto.getId() );
        sqlinjectionAudit.setUserAccount( dto.getUserAccount() );
        sqlinjectionAudit.setSchemaName( dto.getSchemaName() );
        sqlinjectionAudit.setAccessTime( dto.getAccessTime() );
        sqlinjectionAudit.setResponseAction( dto.getResponseAction() );
        sqlinjectionAudit.setSqlStatement( dto.getSqlStatement() );
        sqlinjectionAudit.setIp( dto.getIp() );
        sqlinjectionAudit.setRisk( dto.getRisk() );
        sqlinjectionAudit.setSparefield1( dto.getSparefield1() );
        sqlinjectionAudit.setSparefield2( dto.getSparefield2() );
        sqlinjectionAudit.setSparefield3( dto.getSparefield3() );
        sqlinjectionAudit.setSparefield4( dto.getSparefield4() );
        sqlinjectionAudit.setSparefield5( dto.getSparefield5() );

        return sqlinjectionAudit;
    }

    @Override
    public SqlinjectionAuditDto toDto(SqlinjectionAudit entity) {
        if ( entity == null ) {
            return null;
        }

        SqlinjectionAuditDto sqlinjectionAuditDto = new SqlinjectionAuditDto();

        sqlinjectionAuditDto.setId( entity.getId() );
        sqlinjectionAuditDto.setUserAccount( entity.getUserAccount() );
        sqlinjectionAuditDto.setSchemaName( entity.getSchemaName() );
        sqlinjectionAuditDto.setAccessTime( entity.getAccessTime() );
        sqlinjectionAuditDto.setResponseAction( entity.getResponseAction() );
        sqlinjectionAuditDto.setSqlStatement( entity.getSqlStatement() );
        sqlinjectionAuditDto.setIp( entity.getIp() );
        sqlinjectionAuditDto.setRisk( entity.getRisk() );
        sqlinjectionAuditDto.setSparefield1( entity.getSparefield1() );
        sqlinjectionAuditDto.setSparefield2( entity.getSparefield2() );
        sqlinjectionAuditDto.setSparefield3( entity.getSparefield3() );
        sqlinjectionAuditDto.setSparefield4( entity.getSparefield4() );
        sqlinjectionAuditDto.setSparefield5( entity.getSparefield5() );

        return sqlinjectionAuditDto;
    }

    @Override
    public List<SqlinjectionAudit> toEntity(List<SqlinjectionAuditDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<SqlinjectionAudit> list = new ArrayList<SqlinjectionAudit>( dtoList.size() );
        for ( SqlinjectionAuditDto sqlinjectionAuditDto : dtoList ) {
            list.add( toEntity( sqlinjectionAuditDto ) );
        }

        return list;
    }

    @Override
    public List<SqlinjectionAuditDto> toDto(List<SqlinjectionAudit> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<SqlinjectionAuditDto> list = new ArrayList<SqlinjectionAuditDto>( entityList.size() );
        for ( SqlinjectionAudit sqlinjectionAudit : entityList ) {
            list.add( toDto( sqlinjectionAudit ) );
        }

        return list;
    }
}
