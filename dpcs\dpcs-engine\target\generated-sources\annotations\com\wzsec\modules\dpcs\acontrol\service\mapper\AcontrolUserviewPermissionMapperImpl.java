package com.wzsec.modules.dpcs.acontrol.service.mapper;

import com.wzsec.modules.dpcs.acontrol.domain.AcontrolUserview;
import com.wzsec.modules.dpcs.acontrol.service.dto.AcontrolUserviewPermissionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class AcontrolUserviewPermissionMapperImpl implements AcontrolUserviewPermissionMapper {

    @Override
    public AcontrolUserview toEntity(AcontrolUserviewPermissionDto dto) {
        if ( dto == null ) {
            return null;
        }

        AcontrolUserview acontrolUserview = new AcontrolUserview();

        acontrolUserview.setUsername( dto.getUsername() );
        acontrolUserview.setSchemaName( dto.getSchemaName() );

        return acontrolUserview;
    }

    @Override
    public AcontrolUserviewPermissionDto toDto(AcontrolUserview entity) {
        if ( entity == null ) {
            return null;
        }

        AcontrolUserviewPermissionDto acontrolUserviewPermissionDto = new AcontrolUserviewPermissionDto();

        acontrolUserviewPermissionDto.setUsername( entity.getUsername() );
        acontrolUserviewPermissionDto.setSchemaName( entity.getSchemaName() );

        return acontrolUserviewPermissionDto;
    }

    @Override
    public List<AcontrolUserview> toEntity(List<AcontrolUserviewPermissionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<AcontrolUserview> list = new ArrayList<AcontrolUserview>( dtoList.size() );
        for ( AcontrolUserviewPermissionDto acontrolUserviewPermissionDto : dtoList ) {
            list.add( toEntity( acontrolUserviewPermissionDto ) );
        }

        return list;
    }

    @Override
    public List<AcontrolUserviewPermissionDto> toDto(List<AcontrolUserview> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<AcontrolUserviewPermissionDto> list = new ArrayList<AcontrolUserviewPermissionDto>( entityList.size() );
        for ( AcontrolUserview acontrolUserview : entityList ) {
            list.add( toDto( acontrolUserview ) );
        }

        return list;
    }
}
