package com.wzsec.modules.dpcs.mask.service.mapper;

import com.wzsec.modules.dpcs.mask.domain.MaskStrategyFileUnformatSub;
import com.wzsec.modules.dpcs.mask.service.dto.MaskStrategyFileUnformatSubDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MaskStrategyFileUnformatSubMapperImpl implements MaskStrategyFileUnformatSubMapper {

    @Override
    public MaskStrategyFileUnformatSub toEntity(MaskStrategyFileUnformatSubDto dto) {
        if ( dto == null ) {
            return null;
        }

        MaskStrategyFileUnformatSub maskStrategyFileUnformatSub = new MaskStrategyFileUnformatSub();

        maskStrategyFileUnformatSub.setId( dto.getId() );
        maskStrategyFileUnformatSub.setStrategyid( dto.getStrategyid() );
        maskStrategyFileUnformatSub.setDataname( dto.getDataname() );
        maskStrategyFileUnformatSub.setSenruleid( dto.getSenruleid() );
        maskStrategyFileUnformatSub.setMaskruleid( dto.getMaskruleid() );
        maskStrategyFileUnformatSub.setAlgorithmid( dto.getAlgorithmid() );
        maskStrategyFileUnformatSub.setParam( dto.getParam() );
        maskStrategyFileUnformatSub.setSecretkey( dto.getSecretkey() );
        maskStrategyFileUnformatSub.setSparefield1( dto.getSparefield1() );
        maskStrategyFileUnformatSub.setSparefield2( dto.getSparefield2() );
        maskStrategyFileUnformatSub.setSparefield3( dto.getSparefield3() );
        maskStrategyFileUnformatSub.setSparefield4( dto.getSparefield4() );

        return maskStrategyFileUnformatSub;
    }

    @Override
    public MaskStrategyFileUnformatSubDto toDto(MaskStrategyFileUnformatSub entity) {
        if ( entity == null ) {
            return null;
        }

        MaskStrategyFileUnformatSubDto maskStrategyFileUnformatSubDto = new MaskStrategyFileUnformatSubDto();

        maskStrategyFileUnformatSubDto.setId( entity.getId() );
        maskStrategyFileUnformatSubDto.setStrategyid( entity.getStrategyid() );
        maskStrategyFileUnformatSubDto.setDataname( entity.getDataname() );
        maskStrategyFileUnformatSubDto.setSenruleid( entity.getSenruleid() );
        maskStrategyFileUnformatSubDto.setMaskruleid( entity.getMaskruleid() );
        maskStrategyFileUnformatSubDto.setAlgorithmid( entity.getAlgorithmid() );
        maskStrategyFileUnformatSubDto.setParam( entity.getParam() );
        maskStrategyFileUnformatSubDto.setSecretkey( entity.getSecretkey() );
        maskStrategyFileUnformatSubDto.setSparefield1( entity.getSparefield1() );
        maskStrategyFileUnformatSubDto.setSparefield2( entity.getSparefield2() );
        maskStrategyFileUnformatSubDto.setSparefield3( entity.getSparefield3() );
        maskStrategyFileUnformatSubDto.setSparefield4( entity.getSparefield4() );

        return maskStrategyFileUnformatSubDto;
    }

    @Override
    public List<MaskStrategyFileUnformatSub> toEntity(List<MaskStrategyFileUnformatSubDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MaskStrategyFileUnformatSub> list = new ArrayList<MaskStrategyFileUnformatSub>( dtoList.size() );
        for ( MaskStrategyFileUnformatSubDto maskStrategyFileUnformatSubDto : dtoList ) {
            list.add( toEntity( maskStrategyFileUnformatSubDto ) );
        }

        return list;
    }

    @Override
    public List<MaskStrategyFileUnformatSubDto> toDto(List<MaskStrategyFileUnformatSub> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MaskStrategyFileUnformatSubDto> list = new ArrayList<MaskStrategyFileUnformatSubDto>( entityList.size() );
        for ( MaskStrategyFileUnformatSub maskStrategyFileUnformatSub : entityList ) {
            list.add( toDto( maskStrategyFileUnformatSub ) );
        }

        return list;
    }
}
