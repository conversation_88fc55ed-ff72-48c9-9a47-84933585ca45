package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.PermissionUser;
import com.wzsec.modules.dpcs.cuser.service.dto.PermissionUserDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class PermissionUserMapperImpl implements PermissionUserMapper {

    @Override
    public PermissionUser toEntity(PermissionUserDto dto) {
        if ( dto == null ) {
            return null;
        }

        PermissionUser permissionUser = new PermissionUser();

        permissionUser.setId( dto.getId() );
        permissionUser.setUsername( dto.getUsername() );
        permissionUser.setNickname( dto.getNickname() );
        permissionUser.setPassword( dto.getPassword() );
        permissionUser.setPhone( dto.getPhone() );
        permissionUser.setEmail( dto.getEmail() );
        permissionUser.setEnabled( dto.getEnabled() );
        permissionUser.setEffectiveTimeStart( dto.getEffectiveTimeStart() );
        permissionUser.setEffectiveTimeEnd( dto.getEffectiveTimeEnd() );
        permissionUser.setAccountType( dto.getAccountType() );
        permissionUser.setDeptId( dto.getDeptId() );
        permissionUser.setDeptName( dto.getDeptName() );
        permissionUser.setUserUnit( dto.getUserUnit() );
        permissionUser.setCreateTime( dto.getCreateTime() );
        permissionUser.setCreateId( dto.getCreateId() );
        permissionUser.setCreateNickname( dto.getCreateNickname() );
        permissionUser.setUpdateTime( dto.getUpdateTime() );
        permissionUser.setUpdateId( dto.getUpdateId() );
        permissionUser.setUpdateNickname( dto.getUpdateNickname() );
        permissionUser.setSparefield1( dto.getSparefield1() );
        permissionUser.setSparefield2( dto.getSparefield2() );
        permissionUser.setSparefield3( dto.getSparefield3() );
        permissionUser.setSparefield4( dto.getSparefield4() );
        permissionUser.setSparefield5( dto.getSparefield5() );

        return permissionUser;
    }

    @Override
    public PermissionUserDto toDto(PermissionUser entity) {
        if ( entity == null ) {
            return null;
        }

        PermissionUserDto permissionUserDto = new PermissionUserDto();

        permissionUserDto.setId( entity.getId() );
        permissionUserDto.setUsername( entity.getUsername() );
        permissionUserDto.setNickname( entity.getNickname() );
        permissionUserDto.setPassword( entity.getPassword() );
        permissionUserDto.setPhone( entity.getPhone() );
        permissionUserDto.setEmail( entity.getEmail() );
        permissionUserDto.setEnabled( entity.getEnabled() );
        permissionUserDto.setEffectiveTimeStart( entity.getEffectiveTimeStart() );
        permissionUserDto.setEffectiveTimeEnd( entity.getEffectiveTimeEnd() );
        permissionUserDto.setAccountType( entity.getAccountType() );
        permissionUserDto.setDeptId( entity.getDeptId() );
        permissionUserDto.setDeptName( entity.getDeptName() );
        permissionUserDto.setUserUnit( entity.getUserUnit() );
        permissionUserDto.setCreateTime( entity.getCreateTime() );
        permissionUserDto.setCreateId( entity.getCreateId() );
        permissionUserDto.setCreateNickname( entity.getCreateNickname() );
        permissionUserDto.setUpdateTime( entity.getUpdateTime() );
        permissionUserDto.setUpdateId( entity.getUpdateId() );
        permissionUserDto.setUpdateNickname( entity.getUpdateNickname() );
        permissionUserDto.setSparefield1( entity.getSparefield1() );
        permissionUserDto.setSparefield2( entity.getSparefield2() );
        permissionUserDto.setSparefield3( entity.getSparefield3() );
        permissionUserDto.setSparefield4( entity.getSparefield4() );
        permissionUserDto.setSparefield5( entity.getSparefield5() );

        return permissionUserDto;
    }

    @Override
    public List<PermissionUser> toEntity(List<PermissionUserDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<PermissionUser> list = new ArrayList<PermissionUser>( dtoList.size() );
        for ( PermissionUserDto permissionUserDto : dtoList ) {
            list.add( toEntity( permissionUserDto ) );
        }

        return list;
    }

    @Override
    public List<PermissionUserDto> toDto(List<PermissionUser> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<PermissionUserDto> list = new ArrayList<PermissionUserDto>( entityList.size() );
        for ( PermissionUser permissionUser : entityList ) {
            list.add( toDto( permissionUser ) );
        }

        return list;
    }
}
