package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserSchemarolepermission;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserSchemarolepermissionDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserSchemarolepermissionMapperImpl implements TCuserSchemarolepermissionMapper {

    @Override
    public List<TCuserSchemarolepermission> toEntity(List<TCuserSchemarolepermissionDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserSchemarolepermission> list = new ArrayList<TCuserSchemarolepermission>( dtoList.size() );
        for ( TCuserSchemarolepermissionDto tCuserSchemarolepermissionDto : dtoList ) {
            list.add( toEntity( tCuserSchemarolepermissionDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserSchemarolepermissionDto> toDto(List<TCuserSchemarolepermission> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserSchemarolepermissionDto> list = new ArrayList<TCuserSchemarolepermissionDto>( entityList.size() );
        for ( TCuserSchemarolepermission tCuserSchemarolepermission : entityList ) {
            list.add( toDto( tCuserSchemarolepermission ) );
        }

        return list;
    }
}
