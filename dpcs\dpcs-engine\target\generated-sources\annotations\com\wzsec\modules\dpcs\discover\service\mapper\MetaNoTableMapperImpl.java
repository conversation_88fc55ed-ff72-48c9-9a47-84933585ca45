package com.wzsec.modules.dpcs.discover.service.mapper;

import com.wzsec.modules.dpcs.discover.domain.MetaNoTable;
import com.wzsec.modules.dpcs.discover.service.dto.MetaNoTableDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:53+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class MetaNoTableMapperImpl implements MetaNoTableMapper {

    @Override
    public MetaNoTable toEntity(MetaNoTableDto dto) {
        if ( dto == null ) {
            return null;
        }

        MetaNoTable metaNoTable = new MetaNoTable();

        metaNoTable.setId( dto.getId() );
        metaNoTable.setTaskname( dto.getTaskname() );
        metaNoTable.setIP( dto.getIP() );
        metaNoTable.setPort( dto.getPort() );
        metaNoTable.setSourcetype( dto.getSourcetype() );
        metaNoTable.setSourcename( dto.getSourcename() );
        metaNoTable.setSourceid( dto.getSourceid() );
        metaNoTable.setCreateuser( dto.getCreateuser() );
        metaNoTable.setCreatetime( dto.getCreatetime() );
        metaNoTable.setUpdateuser( dto.getUpdateuser() );
        metaNoTable.setUpdatetime( dto.getUpdatetime() );
        metaNoTable.setSparefield1( dto.getSparefield1() );
        metaNoTable.setSparefield2( dto.getSparefield2() );
        metaNoTable.setSparefield3( dto.getSparefield3() );
        metaNoTable.setSparefield4( dto.getSparefield4() );

        return metaNoTable;
    }

    @Override
    public MetaNoTableDto toDto(MetaNoTable entity) {
        if ( entity == null ) {
            return null;
        }

        MetaNoTableDto metaNoTableDto = new MetaNoTableDto();

        metaNoTableDto.setId( entity.getId() );
        metaNoTableDto.setTaskname( entity.getTaskname() );
        metaNoTableDto.setIP( entity.getIP() );
        metaNoTableDto.setPort( entity.getPort() );
        metaNoTableDto.setSourcetype( entity.getSourcetype() );
        metaNoTableDto.setSourcename( entity.getSourcename() );
        metaNoTableDto.setSourceid( entity.getSourceid() );
        metaNoTableDto.setCreateuser( entity.getCreateuser() );
        metaNoTableDto.setCreatetime( entity.getCreatetime() );
        metaNoTableDto.setUpdateuser( entity.getUpdateuser() );
        metaNoTableDto.setUpdatetime( entity.getUpdatetime() );
        metaNoTableDto.setSparefield1( entity.getSparefield1() );
        metaNoTableDto.setSparefield2( entity.getSparefield2() );
        metaNoTableDto.setSparefield3( entity.getSparefield3() );
        metaNoTableDto.setSparefield4( entity.getSparefield4() );

        return metaNoTableDto;
    }

    @Override
    public List<MetaNoTable> toEntity(List<MetaNoTableDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<MetaNoTable> list = new ArrayList<MetaNoTable>( dtoList.size() );
        for ( MetaNoTableDto metaNoTableDto : dtoList ) {
            list.add( toEntity( metaNoTableDto ) );
        }

        return list;
    }

    @Override
    public List<MetaNoTableDto> toDto(List<MetaNoTable> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<MetaNoTableDto> list = new ArrayList<MetaNoTableDto>( entityList.size() );
        for ( MetaNoTable metaNoTable : entityList ) {
            list.add( toDto( metaNoTable ) );
        }

        return list;
    }
}
