package com.wzsec.modules.dpcs.cuser.service.mapper;

import com.wzsec.modules.dpcs.cuser.domain.TCuserIpblackwhite;
import com.wzsec.modules.dpcs.cuser.service.dto.TCuserIpblackwhiteDto;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-28T08:21:54+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_211 (Oracle Corporation)"
)
@Component
public class TCuserIpblackwhiteMapperImpl implements TCuserIpblackwhiteMapper {

    @Override
    public TCuserIpblackwhite toEntity(TCuserIpblackwhiteDto dto) {
        if ( dto == null ) {
            return null;
        }

        TCuserIpblackwhite tCuserIpblackwhite = new TCuserIpblackwhite();

        tCuserIpblackwhite.setId( dto.getId() );
        tCuserIpblackwhite.setIp( dto.getIp() );
        tCuserIpblackwhite.setAccessType( dto.getAccessType() );
        tCuserIpblackwhite.setSchemaroleId( dto.getSchemaroleId() );
        tCuserIpblackwhite.setUserId( dto.getUserId() );
        tCuserIpblackwhite.setRoleId( dto.getRoleId() );
        tCuserIpblackwhite.setCreateTime( dto.getCreateTime() );
        tCuserIpblackwhite.setCreateId( dto.getCreateId() );
        tCuserIpblackwhite.setCreateNickname( dto.getCreateNickname() );
        tCuserIpblackwhite.setUpdateTime( dto.getUpdateTime() );
        tCuserIpblackwhite.setUpdateId( dto.getUpdateId() );
        tCuserIpblackwhite.setUpdateNickname( dto.getUpdateNickname() );
        tCuserIpblackwhite.setSparefield1( dto.getSparefield1() );
        tCuserIpblackwhite.setSparefield2( dto.getSparefield2() );
        tCuserIpblackwhite.setSparefield3( dto.getSparefield3() );
        tCuserIpblackwhite.setSparefield4( dto.getSparefield4() );
        tCuserIpblackwhite.setSparefield5( dto.getSparefield5() );

        return tCuserIpblackwhite;
    }

    @Override
    public TCuserIpblackwhiteDto toDto(TCuserIpblackwhite entity) {
        if ( entity == null ) {
            return null;
        }

        TCuserIpblackwhiteDto tCuserIpblackwhiteDto = new TCuserIpblackwhiteDto();

        tCuserIpblackwhiteDto.setId( entity.getId() );
        tCuserIpblackwhiteDto.setIp( entity.getIp() );
        tCuserIpblackwhiteDto.setAccessType( entity.getAccessType() );
        tCuserIpblackwhiteDto.setSchemaroleId( entity.getSchemaroleId() );
        tCuserIpblackwhiteDto.setUserId( entity.getUserId() );
        tCuserIpblackwhiteDto.setRoleId( entity.getRoleId() );
        tCuserIpblackwhiteDto.setCreateTime( entity.getCreateTime() );
        tCuserIpblackwhiteDto.setCreateId( entity.getCreateId() );
        tCuserIpblackwhiteDto.setCreateNickname( entity.getCreateNickname() );
        tCuserIpblackwhiteDto.setUpdateTime( entity.getUpdateTime() );
        tCuserIpblackwhiteDto.setUpdateId( entity.getUpdateId() );
        tCuserIpblackwhiteDto.setUpdateNickname( entity.getUpdateNickname() );
        tCuserIpblackwhiteDto.setSparefield1( entity.getSparefield1() );
        tCuserIpblackwhiteDto.setSparefield2( entity.getSparefield2() );
        tCuserIpblackwhiteDto.setSparefield3( entity.getSparefield3() );
        tCuserIpblackwhiteDto.setSparefield4( entity.getSparefield4() );
        tCuserIpblackwhiteDto.setSparefield5( entity.getSparefield5() );

        return tCuserIpblackwhiteDto;
    }

    @Override
    public List<TCuserIpblackwhite> toEntity(List<TCuserIpblackwhiteDto> dtoList) {
        if ( dtoList == null ) {
            return null;
        }

        List<TCuserIpblackwhite> list = new ArrayList<TCuserIpblackwhite>( dtoList.size() );
        for ( TCuserIpblackwhiteDto tCuserIpblackwhiteDto : dtoList ) {
            list.add( toEntity( tCuserIpblackwhiteDto ) );
        }

        return list;
    }

    @Override
    public List<TCuserIpblackwhiteDto> toDto(List<TCuserIpblackwhite> entityList) {
        if ( entityList == null ) {
            return null;
        }

        List<TCuserIpblackwhiteDto> list = new ArrayList<TCuserIpblackwhiteDto>( entityList.size() );
        for ( TCuserIpblackwhite tCuserIpblackwhite : entityList ) {
            list.add( toDto( tCuserIpblackwhite ) );
        }

        return list;
    }
}
